import 'dart:convert';
import 'package:http/http.dart' as http;

Future<void> main() async {
  print('🔍 Testing User-Agent Fix for ZoomFresh Admin API');
  print('================================================');
  
  await testWithCurlUserAgent();
  await testWithoutUserAgent();
}

Future<void> testWithCurlUserAgent() async {
  print('\n📱 TEST 1: With curl User-Agent');
  print('--------------------------------');
  
  try {
    final url = Uri.parse('https://zoomfresh.co.in/api/admin/login');
    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'curl/8.7.1', // Match curl's User-Agent
      },
      body: jsonEncode({
        'email': '<EMAIL>',
        'password': 'password123',
        'fcm_token': 'test_token',
      }),
    ).timeout(Duration(seconds: 30));

    print('   ✅ Status Code: ${response.statusCode}');
    print('   📄 Response: ${response.body.substring(0, 100)}...');
    
    if (response.statusCode == 200) {
      print('   🎉 SUCCESS: User-Agent fix works!');
    } else {
      print('   ⚠️  Unexpected status: ${response.statusCode}');
    }
  } catch (e) {
    print('   ❌ ERROR: $e');
  }
}

Future<void> testWithoutUserAgent() async {
  print('\n📱 TEST 2: Without User-Agent (original)');
  print('----------------------------------------');
  
  try {
    final url = Uri.parse('https://zoomfresh.co.in/api/admin/login');
    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        // No User-Agent header
      },
      body: jsonEncode({
        'email': '<EMAIL>',
        'password': 'password123',
        'fcm_token': 'test_token',
      }),
    ).timeout(Duration(seconds: 30));

    print('   ✅ Status Code: ${response.statusCode}');
    print('   📄 Response: ${response.body.substring(0, 100)}...');
  } catch (e) {
    print('   ❌ ERROR: $e');
  }
}
