import 'dart:async';
import 'dart:convert';
import 'dart:io';

Future<void> main() async {
  print('🔍 Testing TLS Handshake Details');
  print('================================');

  await testTLSHandshake();
  await testHTTPSWithSecureSocket();
}

Future<void> testTLSHandshake() async {
  print('\n📱 TEST 1: TLS Handshake Analysis');
  print('=================================');

  try {
    print('   🔄 Establishing secure socket connection...');

    final socket = await SecureSocket.connect(
      'zoomfresh.co.in',
      443,
      timeout: Duration(seconds: 30),
      onBadCertificate: (certificate) {
        print('   🔒 TLS Certificate Details:');
        print('      Subject: ${certificate.subject}');
        print('      Issuer: ${certificate.issuer}');
        print('      Start Date: ${certificate.startValidity}');
        print('      End Date: ${certificate.endValidity}');
        return true; // Accept certificate
      },
    );

    print('   ✅ TLS handshake successful!');
    print('   📊 Selected Protocol: ${socket.selectedProtocol}');
    print('   📊 Remote Address: ${socket.remoteAddress}');
    print('   📊 Remote Port: ${socket.remotePort}');

    // Try to send a simple HTTP request manually
    print('   🔄 Sending manual HTTP request...');

    final httpRequest = '''POST /api/admin/login HTTP/1.1\r
Host: zoomfresh.co.in\r
User-Agent: curl/8.7.1\r
Content-Type: application/json\r
Accept: application/json\r
Content-Length: 77\r
Connection: close\r
\r
{"email":"<EMAIL>","password":"password123","fcm_token":"test_token"}''';

    socket.write(httpRequest);
    await socket.flush();

    print('   🔄 Reading response...');

    final responseBytes = await socket.toList();
    final response = utf8.decode(responseBytes.expand((x) => x).toList());
    print('   📄 Response received:');
    print('   ${response.substring(0, 200)}...');

    socket.destroy();
  } catch (e) {
    print('   ❌ TLS handshake failed: $e');
    if (e is SocketException) {
      print('   📊 Socket Error: ${e.message}');
      print('   📊 OS Error: ${e.osError}');
    }
  }
}

Future<void> testHTTPSWithSecureSocket() async {
  print('\n📱 TEST 2: HTTPS with Manual SecureSocket');
  print('=========================================');

  try {
    print('   🔄 Creating secure socket...');

    final socket = await SecureSocket.connect(
      'zoomfresh.co.in',
      443,
      timeout: Duration(seconds: 30),
      supportedProtocols: ['h2', 'http/1.1'], // Try to negotiate HTTP/2
      onBadCertificate: (certificate) => true,
    );

    print('   ✅ Secure socket connected!');
    print('   📊 Selected Protocol: ${socket.selectedProtocol}');

    // Send HTTP/1.1 request (since HTTP/2 is binary and more complex)
    final requestBody = jsonEncode({
      'email': '<EMAIL>',
      'password': 'password123',
      'fcm_token': 'test_token',
    });

    final httpRequest = [
      'POST /api/admin/login HTTP/1.1',
      'Host: zoomfresh.co.in',
      'User-Agent: curl/8.7.1',
      'Content-Type: application/json',
      'Accept: application/json',
      'Content-Length: ${requestBody.length}',
      'Connection: close',
      '',
      requestBody,
    ].join('\r\n');

    print('   🔄 Sending HTTP request...');
    socket.write(httpRequest);
    await socket.flush();

    print('   🔄 Waiting for response...');

    // Read response with timeout
    final responseBytes = <int>[];
    final completer = Completer<void>();

    socket.listen(
      (data) {
        responseBytes.addAll(data);
        final partial = utf8.decode(responseBytes);
        // Break if we get a complete response
        if (partial.contains('\r\n\r\n')) {
          completer.complete();
        }
      },
      onDone: () => completer.complete(),
      onError: (error) => completer.completeError(error),
    );

    // Wait for response or timeout
    await completer.future.timeout(Duration(seconds: 10));
    final response = utf8.decode(responseBytes);
    print('   📄 Response received (${response.length} chars):');

    if (response.isNotEmpty) {
      final lines = response.split('\r\n');
      print('   📊 Status Line: ${lines.isNotEmpty ? lines[0] : 'No status line'}');

      if (response.contains('HTTP/1.1 200') || response.contains('HTTP/2 200')) {
        print('   🎉 SUCCESS: Manual HTTPS request works!');
      } else {
        print('   ⚠️  Unexpected response: ${lines.isNotEmpty ? lines[0] : 'Empty response'}');
      }
    } else {
      print('   ❌ No response received');
    }

    socket.destroy();
  } catch (e) {
    print('   ❌ Manual HTTPS failed: $e');
  }
}
