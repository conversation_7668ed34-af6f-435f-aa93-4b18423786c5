import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';

Future<void> main() async {
  print('🔍 Comprehensive TLS/HTTP Fix Testing');
  print('=====================================');

  await testWithProxyBypass();
  await testWithDifferentTLSSettings();
  await testWithHTTPFallback();
  await testWithCurlCommand();
}

Future<void> testWithProxyBypass() async {
  print('\n📱 TEST 1: Bypass Proxy and Use Direct Connection');
  print('=================================================');

  try {
    final dio = Dio();

    // Configure timeouts
    dio.options.connectTimeout = const Duration(seconds: 30);
    dio.options.receiveTimeout = const Duration(seconds: 30);
    dio.options.sendTimeout = const Duration(seconds: 30);

    // Headers to match curl exactly
    dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'curl/8.7.1',
      'Host': 'zoomfresh.co.in',
      'Connection': 'close', // Force connection close like curl
    };

    // Configure HTTP client to bypass any proxy and use direct connection
    (dio.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () {
      final client = HttpClient();

      // Disable proxy
      client.findProxy = (uri) => 'DIRECT';

      // Connection settings
      client.connectionTimeout = const Duration(seconds: 30);
      client.idleTimeout = const Duration(seconds: 30);

      // Accept all certificates
      client.badCertificateCallback = (cert, host, port) => true;

      return client;
    };

    print('   🔄 Making API call with proxy bypass...');

    final response = await dio.post(
      'https://zoomfresh.co.in/api/admin/login',
      data: {
        'email': '<EMAIL>',
        'password': 'password123',
        'fcm_token': 'test_token',
      },
    );

    print('   ✅ Status Code: ${response.statusCode}');
    if (response.statusCode == 200) {
      print('   🎉 SUCCESS: Proxy bypass works!');
      final data = response.data;
      if (data is Map && data['status'] == 'success') {
        print('   📄 Login successful: ${data['message']}');
      }
    }
  } catch (e) {
    print('   ❌ ERROR: $e');
  }
}

Future<void> testWithDifferentTLSSettings() async {
  print('\n📱 TEST 2: Different TLS Configuration Approaches');
  print('==================================================');

  // Test with different security contexts
  final approaches = [
    'Default Security Context',
    'Relaxed Security Context',
    'Custom Security Context',
  ];

  for (int i = 0; i < approaches.length; i++) {
    print('\n   🔄 Testing ${approaches[i]}...');

    try {
      final dio = Dio();
      dio.options.connectTimeout = const Duration(seconds: 30);
      dio.options.receiveTimeout = const Duration(seconds: 30);

      dio.options.headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'curl/8.7.1',
      };

      (dio.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () {
        final client = HttpClient();

        switch (i) {
          case 0: // Default
            break;
          case 1: // Relaxed
            client.badCertificateCallback = (cert, host, port) => true;
            break;
          case 2: // Custom
            client.badCertificateCallback = (cert, host, port) => true;
            client.connectionTimeout = const Duration(seconds: 60);
            client.idleTimeout = const Duration(seconds: 60);
            break;
        }

        return client;
      };

      final response = await dio.post(
        'https://zoomfresh.co.in/api/admin/login',
        data: {
          'email': '<EMAIL>',
          'password': 'password123',
          'fcm_token': 'test_token',
        },
      );

      print('      ✅ SUCCESS with ${approaches[i]}! Status: ${response.statusCode}');
      return; // If one works, we're done
    } catch (e) {
      print('      ❌ Failed with ${approaches[i]}: ${e.toString().substring(0, 100)}...');
    }
  }
}

Future<void> testWithHTTPFallback() async {
  print('\n📱 TEST 3: HTTP Fallback (Non-SSL)');
  print('===================================');

  try {
    print('   ⚠️  Testing HTTP (non-SSL) as fallback...');

    final dio = Dio();
    dio.options.connectTimeout = const Duration(seconds: 30);
    dio.options.receiveTimeout = const Duration(seconds: 30);

    dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'curl/8.7.1',
      'Host': 'zoomfresh.co.in',
    };

    // Try HTTP instead of HTTPS
    final response = await dio.post(
      'http://zoomfresh.co.in/api/admin/login',
      data: {
        'email': '<EMAIL>',
        'password': 'password123',
        'fcm_token': 'test_token',
      },
    );

    print('   ✅ Status Code: ${response.statusCode}');
    if (response.statusCode == 200) {
      print('   🎉 SUCCESS: HTTP fallback works!');
      print('   ⚠️  Note: This is not secure (no SSL/TLS)');
    }
  } catch (e) {
    print('   ❌ HTTP fallback failed: $e');
  }
}

// Additional test function to be added
Future<void> testWithCurlCommand() async {
  print('\n📱 TEST 4: Verify Curl Still Works');
  print('===================================');

  try {
    print('   🔄 Running curl command for comparison...');

    final result = await Process.run('curl', [
      '-X',
      'POST',
      'https://zoomfresh.co.in/api/admin/login',
      '-H',
      'Content-Type: application/json',
      '-H',
      'Accept: application/json',
      '-d',
      '{"email":"<EMAIL>","password":"password123","fcm_token":"test_token"}',
      '--max-time',
      '30',
    ]);

    print('   📊 Curl Exit Code: ${result.exitCode}');

    if (result.exitCode == 0) {
      print('   ✅ Curl still works successfully');
      final response = result.stdout.toString();
      if (response.contains('"status":"success"')) {
        print('   🎉 Curl login successful');
      }
    } else {
      print('   ❌ Curl failed: ${result.stderr}');
    }
  } catch (e) {
    print('   ❌ Curl test error: $e');
  }
}
