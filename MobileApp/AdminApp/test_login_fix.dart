import 'dart:io';
import 'package:http/http.dart' as http;
import 'dart:convert';

void main() async {
  print('🔧 Testing Flutter Admin App Login Fix');
  print('=====================================\n');

  // Test 1: Direct API call to verify server accessibility
  print('1. Testing direct API accessibility...');
  await testDirectApiCall();

  // Test 2: Test with Flutter HTTP client (simulating app behavior)
  print('\n2. Testing with Flutter HTTP client...');
  await testFlutterHttpClient();

  print('\n✅ Test completed! Check the results above.');
}

Future<void> testDirectApiCall() async {
  try {
    final url = Uri.parse('https://zoomfresh.co.in/api/admin/login');
    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: jsonEncode({
        'email': '<EMAIL>',
        'password': 'password123',
        'fcm_token': 'test_token',
      }),
    ).timeout(Duration(seconds: 30));

    print('   ✅ API Response Status: ${response.statusCode}');
    print('   📄 Response Body: ${response.body}');
    
    if (response.statusCode == 200 || response.statusCode == 401) {
      print('   🎉 Server is accessible and responding correctly!');
    } else {
      print('   ⚠️  Unexpected status code: ${response.statusCode}');
    }
  } catch (e) {
    print('   ❌ Error: $e');
    if (e.toString().contains('Connection reset by peer')) {
      print('   🔍 This is the original error - network security config issue');
    }
  }
}

Future<void> testFlutterHttpClient() async {
  try {
    final client = http.Client();
    final url = Uri.parse('https://zoomfresh.co.in/api/admin/login');
    
    final response = await client.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: jsonEncode({
        'email': '<EMAIL>',
        'password': 'password123',
        'fcm_token': 'test_token',
      }),
    ).timeout(Duration(seconds: 30));

    print('   ✅ Flutter HTTP Client Status: ${response.statusCode}');
    print('   📄 Response Body: ${response.body}');
    
    client.close();
    
    if (response.statusCode == 200 || response.statusCode == 401) {
      print('   🎉 Flutter HTTP client working correctly!');
    }
  } catch (e) {
    print('   ❌ Flutter HTTP Error: $e');
  }
}
