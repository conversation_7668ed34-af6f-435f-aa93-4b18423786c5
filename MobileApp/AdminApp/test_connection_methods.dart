import 'dart:io';
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';

Future<void> main() async {
  print('🔍 Testing Different Connection Methods');
  print('======================================');
  
  await testWithCustomHttpClient();
  await testWithSocketConnection();
  await testWithDifferentPorts();
}

Future<void> testWithCustomHttpClient() async {
  print('\n📱 TEST 1: Custom HttpClient with Detailed Logging');
  print('==================================================');
  
  try {
    final client = HttpClient();
    
    // Configure client to match curl behavior as closely as possible
    client.connectionTimeout = const Duration(seconds: 30);
    client.idleTimeout = const Duration(seconds: 30);
    
    // Enable detailed SSL logging
    client.badCertificateCallback = (cert, host, port) {
      print('   🔒 SSL Handshake for $host:$port');
      print('      Certificate Subject: ${cert.subject}');
      print('      Certificate Issuer: ${cert.issuer}');
      return true;
    };
    
    print('   🔄 Creating HTTP request...');
    
    final request = await client.postUrl(Uri.parse('https://zoomfresh.co.in/api/admin/login'));
    
    // Set headers to match curl exactly
    request.headers.set('Content-Type', 'application/json');
    request.headers.set('Accept', 'application/json');
    request.headers.set('User-Agent', 'curl/8.7.1');
    request.headers.set('Host', 'zoomfresh.co.in');
    
    // Add request body
    final body = jsonEncode({
      'email': '<EMAIL>',
      'password': 'password123',
      'fcm_token': 'test_token',
    });
    
    request.write(body);
    
    print('   🔄 Sending request...');
    final response = await request.close();
    
    print('   ✅ Status Code: ${response.statusCode}');
    
    if (response.statusCode == 200) {
      print('   🎉 SUCCESS: Custom HttpClient works!');
      final responseBody = await response.transform(utf8.decoder).join();
      print('   📄 Response: ${responseBody.substring(0, 100)}...');
    } else {
      print('   ⚠️  Unexpected status: ${response.statusCode}');
    }
    
    client.close();
  } catch (e) {
    print('   ❌ ERROR: $e');
    if (e is SocketException) {
      print('   📊 Socket Error Details:');
      print('      Message: ${e.message}');
      print('      OS Error: ${e.osError}');
      print('      Address: ${e.address}');
      print('      Port: ${e.port}');
    }
  }
}

Future<void> testWithSocketConnection() async {
  print('\n📱 TEST 2: Direct Socket Connection Test');
  print('========================================');
  
  try {
    print('   🔄 Testing basic socket connectivity...');
    
    final socket = await Socket.connect('zoomfresh.co.in', 443, timeout: Duration(seconds: 30));
    
    print('   ✅ Socket connected successfully!');
    print('   📊 Remote Address: ${socket.remoteAddress}');
    print('   📊 Remote Port: ${socket.remotePort}');
    print('   📊 Local Address: ${socket.address}');
    print('   📊 Local Port: ${socket.port}');
    
    socket.destroy();
    
  } catch (e) {
    print('   ❌ Socket connection failed: $e');
  }
}

Future<void> testWithDifferentPorts() async {
  print('\n📱 TEST 3: Testing Different Connection Approaches');
  print('==================================================');
  
  // Test 1: Try with explicit port 443
  await _testUrlVariation('https://zoomfresh.co.in:443/api/admin/login', 'Explicit Port 443');
  
  // Test 2: Try with IP address (to bypass DNS issues)
  await _testUrlVariation('https://*************/api/admin/login', 'Direct IP Address');
  
  // Test 3: Try with different timeout settings
  await _testWithDifferentTimeouts();
}

Future<void> _testUrlVariation(String url, String description) async {
  print('\n   🔄 Testing $description: $url');
  
  try {
    final dio = Dio();
    dio.options.connectTimeout = const Duration(seconds: 30);
    dio.options.receiveTimeout = const Duration(seconds: 30);
    
    dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'curl/8.7.1',
      'Host': 'zoomfresh.co.in', // Keep original host header even for IP
    };
    
    final response = await dio.post(
      url,
      data: {
        'email': '<EMAIL>',
        'password': 'password123',
        'fcm_token': 'test_token',
      },
    );
    
    print('      ✅ Status: ${response.statusCode}');
    if (response.statusCode == 200) {
      print('      🎉 SUCCESS with $description!');
    }
  } catch (e) {
    print('      ❌ Failed with $description: $e');
  }
}

Future<void> _testWithDifferentTimeouts() async {
  print('\n   🔄 Testing with Different Timeout Settings');
  
  final timeouts = [5, 10, 30, 60];
  
  for (final timeout in timeouts) {
    try {
      print('      Testing ${timeout}s timeout...');
      
      final dio = Dio();
      dio.options.connectTimeout = Duration(seconds: timeout);
      dio.options.receiveTimeout = Duration(seconds: timeout);
      
      dio.options.headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'curl/8.7.1',
      };
      
      final response = await dio.post(
        'https://zoomfresh.co.in/api/admin/login',
        data: {
          'email': '<EMAIL>',
          'password': 'password123',
          'fcm_token': 'test_token',
        },
      );
      
      print('         ✅ SUCCESS with ${timeout}s timeout! Status: ${response.statusCode}');
      break; // If one works, we found the solution
    } catch (e) {
      print('         ❌ Failed with ${timeout}s timeout');
    }
  }
}
