import 'package:flutter/material.dart';
import 'package:user/services/api_service.dart';
import 'package:user/services/dio_api_service.dart';

class NetworkComparisonTest extends StatefulWidget {
  @override
  _NetworkComparisonTestState createState() => _NetworkComparisonTestState();
}

class _NetworkComparisonTestState extends State<NetworkComparisonTest> {
  String testResult = '';
  bool isRunning = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Network Comparison Test'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          children: [
            ElevatedButton(
              onPressed: isRunning ? null : runComparisonTest,
              child: Text(isRunning ? 'Testing...' : 'Run Network Comparison Test'),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
            ),
            SizedBox(height: 20),
            Expanded(
              child: SingleChildScrollView(
                child: Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    testResult.isEmpty ? 'Click "Run Network Comparison Test" to start...' : testResult,
                    style: TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> runComparisonTest() async {
    setState(() {
      isRunning = true;
      testResult = 'Starting network comparison test...\n\n';
    });

    try {
      // Test 1: HTTP Package
      addResult('🔧 TEST 1: Standard HTTP Package');
      addResult('=====================================');
      
      final httpResult = await ApiService.adminLogin(
        '<EMAIL>',
        'password123',
        fcmToken: 'test_fcm_token',
      );

      addResult('HTTP Result:');
      addResult('  Success: ${httpResult['success']}');
      addResult('  Status Code: ${httpResult['statusCode']}');
      addResult('  Error: ${httpResult['error'] ?? 'None'}');
      if (httpResult['success']) {
        addResult('  ✅ HTTP Package: SUCCESS');
      } else {
        addResult('  ❌ HTTP Package: FAILED');
      }

      addResult('\n');

      // Test 2: Dio Package
      addResult('🔧 TEST 2: Dio HTTP Client');
      addResult('=====================================');
      
      final dioResult = await DioApiService.adminLogin(
        '<EMAIL>',
        'password123',
        fcmToken: 'test_fcm_token',
      );

      addResult('Dio Result:');
      addResult('  Success: ${dioResult['success']}');
      addResult('  Status Code: ${dioResult['statusCode']}');
      addResult('  Error: ${dioResult['error'] ?? 'None'}');
      if (dioResult['success']) {
        addResult('  ✅ Dio Package: SUCCESS');
      } else {
        addResult('  ❌ Dio Package: FAILED');
      }

      addResult('\n');

      // Summary
      addResult('📊 SUMMARY');
      addResult('=====================================');
      
      if (httpResult['success'] && dioResult['success']) {
        addResult('🎉 BOTH CLIENTS WORKING! Network issue resolved.');
      } else if (httpResult['success'] || dioResult['success']) {
        addResult('⚠️  ONE CLIENT WORKING. Partial success.');
        if (dioResult['success'] && !httpResult['success']) {
          addResult('   Recommendation: Switch to Dio for better reliability.');
        }
      } else {
        addResult('❌ BOTH CLIENTS FAILED. Network/server issue persists.');
        addResult('   Check network security configuration and server status.');
      }
      
    } catch (e) {
      addResult('❌ CRITICAL ERROR: $e');
    }

    setState(() {
      isRunning = false;
    });
  }

  void addResult(String message) {
    setState(() {
      testResult += '$message\n';
    });
  }
}
