import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

import '../constants.dart';

class ApiService {
  static const String _tokenKey = 'auth_token';
  static const String _userIdKey = 'user_id';
  static http.Client? _persistentClient;

  // Get or create persistent HTTP client
  static http.Client _getHttpClient() {
    _persistentClient ??= http.Client();
    return _persistentClient!;
  }

  // Close persistent client
  static void closeHttpClient() {
    _persistentClient?.close();
    _persistentClient = null;
  }

  // Get stored authentication token
  static Future<String?> getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }

  // Store authentication token
  static Future<void> setAuthToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, token);
  }

  // Store user ID
  static Future<void> setUserId(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userIdKey, userId);
  }

  // Get stored user ID
  static Future<String?> getUserId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userIdKey);
  }

  // Clear authentication data
  static Future<void> clearAuthData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
    await prefs.remove(_userIdKey);
  }

  // Get headers with authentication and enhanced connection settings
  static Future<Map<String, String>> getHeaders() async {
    final token = await getAuthToken();
    return {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      'User-Agent': 'curl/8.7.1', // Match curl's User-Agent to avoid server filtering
      'x-access-key': '903361',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  // Generic API call method
  static Future<Map<String, dynamic>> makeApiCall({
    required String endpoint,
    required String method,
    Map<String, dynamic>? body,
    bool requiresAuth = true,
  }) async {
    http.Client? client;
    try {
      // Fix URL construction to avoid double slashes
      String cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl.substring(0, baseUrl.length - 1) : baseUrl;
      String cleanEndpoint = endpoint.startsWith('/') ? endpoint : '/$endpoint';
      final url = Uri.parse('$cleanBaseUrl$cleanEndpoint');

      print('API Call: ${url.toString()}'); // Debug: Print the constructed URL
      final headers = await getHeaders();
      print('Headers: $headers'); // Debug: Print headers

      // Use persistent HTTP client with timeout
      client = _getHttpClient();
      http.Response response;

      switch (method.toUpperCase()) {
        case 'GET':
          response = await client.get(url, headers: headers).timeout(const Duration(seconds: 30));
          break;
        case 'POST':
          response = await client
              .post(
                url,
                headers: headers,
                body: body != null ? jsonEncode(body) : null,
              )
              .timeout(const Duration(seconds: 30));
          break;
        case 'PUT':
          response = await client
              .put(
                url,
                headers: headers,
                body: body != null ? jsonEncode(body) : null,
              )
              .timeout(const Duration(seconds: 30));
          break;
        case 'DELETE':
          response = await client.delete(url, headers: headers).timeout(const Duration(seconds: 30));
          break;
        default:
          throw Exception('Unsupported HTTP method: $method');
      }

      print('Response Status: ${response.statusCode}'); // Debug: Print status code
      print('Response Body: ${response.body}'); // Debug: Print response body

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': responseData,
          'statusCode': response.statusCode,
        };
      } else {
        return {
          'success': false,
          'error': responseData['message'] ?? 'Unknown error occurred',
          'statusCode': response.statusCode,
        };
      }
    } on TimeoutException catch (e) {
      print('API Timeout Error: $e');
      return {
        'success': false,
        'error': 'Request timeout. Please check your internet connection and try again.',
        'statusCode': 0,
      };
    } on SocketException catch (e) {
      print('Socket Error: $e');
      String errorMessage = 'Network connection failed. Please check your internet connection.';
      if (e.toString().contains('Connection reset by peer')) {
        errorMessage = 'Connection was reset by the server. Please try again.';
      } else if (e.toString().contains('Connection refused')) {
        errorMessage = 'Cannot connect to server. Please check if the server is running.';
      }
      return {
        'success': false,
        'error': errorMessage,
        'statusCode': 0,
      };
    } catch (e) {
      print('API Error Details: $e');

      // More specific error handling
      String errorMessage = 'Network error: $e';
      if (e.toString().contains('Connection refused')) {
        errorMessage = 'Cannot connect to server. Please check if the server is running on http://127.0.0.1:8000';
      } else if (e.toString().contains('SocketException')) {
        errorMessage = 'Network connection failed. Please check your internet connection.';
      }

      return {
        'success': false,
        'error': errorMessage,
        'statusCode': 0,
      };
    } finally {
      // Don't close persistent client, it will be reused
      // client?.close(); // Commented out for persistent connection
    }
  }

  // Admin Authentication APIs with retry logic
  static Future<Map<String, dynamic>> adminLogin(String email, String password, {String? fcmToken}) async {
    return await _makeApiCallWithRetry(
      endpoint: '/admin/login',
      method: 'POST',
      body: {
        'email': email,
        'password': password,
        if (fcmToken != null) 'fcm_token': fcmToken,
      },
      requiresAuth: false,
      maxRetries: 3,
    );
  }

  // Enhanced API call with retry logic for connection issues
  static Future<Map<String, dynamic>> _makeApiCallWithRetry({
    required String endpoint,
    required String method,
    Map<String, dynamic>? body,
    bool requiresAuth = true,
    int maxRetries = 3,
  }) async {
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      print('API Call attempt $attempt/$maxRetries');

      final result = await makeApiCall(
        endpoint: endpoint,
        method: method,
        body: body,
        requiresAuth: requiresAuth,
      );

      // If successful, return immediately
      if (result['success']) {
        return result;
      }

      // Check if it's a connection reset error
      final error = result['error'].toString();
      if (error.contains('Connection reset by peer') || error.contains('SocketException')) {
        if (attempt < maxRetries) {
          final delaySeconds = attempt * 2;
          print('🔄 Connection issue detected, retrying in $delaySeconds seconds... (Attempt $attempt/$maxRetries)');
          await Future.delayed(Duration(seconds: delaySeconds));
          continue;
        } else {
          print('❌ All retry attempts failed. Connection issues persist.');
        }
      }

      // For non-connection errors or final attempt, return the error
      return result;
    }

    // This should never be reached, but just in case
    return {
      'success': false,
      'error': 'All retry attempts failed',
      'statusCode': 0,
    };
  }

  static Future<Map<String, dynamic>> validateAdmin() async {
    return await makeApiCall(
      endpoint: '/admin/validate',
      method: 'GET',
    );
  }

  // User Management APIs
  static Future<Map<String, dynamic>> getUsers() async {
    return await makeApiCall(
      endpoint: '/users',
      method: 'GET',
    );
  }

  // Product Management APIs
  static Future<Map<String, dynamic>> getProducts({int? categoryId}) async {
    String endpoint = '/products';
    if (categoryId != null) {
      endpoint += '?category_id=$categoryId';
    }
    return await makeApiCall(
      endpoint: endpoint,
      method: 'GET',
    );
  }

  static Future<Map<String, dynamic>> updateProductPrice(int productId, double price, {double? salePrice}) async {
    return await makeApiCall(
      endpoint: '/products/price-update',
      method: 'POST',
      body: {
        'product_id': productId,
        'price': price,
        if (salePrice != null) 'sale_price': salePrice,
      },
    );
  }

  // Category Management APIs
  static Future<Map<String, dynamic>> getCategories() async {
    return await makeApiCall(
      endpoint: '/categories',
      method: 'GET',
    );
  }

  // Subscription Management APIs
  static Future<Map<String, dynamic>> getSubscriptionPackages() async {
    return await makeApiCall(
      endpoint: '/subscriptions/packages',
      method: 'GET',
    );
  }

  static Future<Map<String, dynamic>> getSubscribedUsers() async {
    return await makeApiCall(
      endpoint: '/subscriptions/users',
      method: 'GET',
    );
  }

  static Future<Map<String, dynamic>> addSubscriptionPackage(String title, double price, int numberOfMonths) async {
    return await makeApiCall(
      endpoint: '/subscriptions/add',
      method: 'POST',
      body: {
        'title': title,
        'price': price,
        'number_of_month': numberOfMonths,
      },
    );
  }

  static Future<Map<String, dynamic>> deleteSubscriptionPackage(int packageId) async {
    return await makeApiCall(
      endpoint: '/subscriptions/delete',
      method: 'POST',
      body: {
        'id': packageId,
      },
    );
  }

  // Flash Sale Management APIs
  static Future<Map<String, dynamic>> getFlashSaleProducts() async {
    return await makeApiCall(
      endpoint: '/flash-sales/products',
      method: 'GET',
    );
  }

  static Future<Map<String, dynamic>> getActiveFlashSaleProducts() async {
    return await makeApiCall(
      endpoint: '/flash-sales/active-products',
      method: 'GET',
    );
  }

  static Future<Map<String, dynamic>> getFlashSaleDates() async {
    return await makeApiCall(
      endpoint: '/flash-sales/dates',
      method: 'GET',
    );
  }

  static Future<Map<String, dynamic>> updateFlashSaleDates(String startDate, String endDate, String startTime, String endTime) async {
    return await makeApiCall(
      endpoint: '/flash-sales/update-dates',
      method: 'POST',
      body: {
        'start_date': startDate,
        'end_date': endDate,
        'start_time': startTime,
        'end_time': endTime,
      },
    );
  }

  static Future<Map<String, dynamic>> toggleFlashSaleProduct(int productId, int status) async {
    return await makeApiCall(
      endpoint: '/flash-sales/toggle-product',
      method: 'POST',
      body: {
        'product_id': productId,
        'status': status,
      },
    );
  }

  // Order Management APIs
  static Future<Map<String, dynamic>> getOrders() async {
    return await makeApiCall(
      endpoint: '/orders',
      method: 'GET',
    );
  }

  static Future<Map<String, dynamic>> getOrderDetails(int orderId) async {
    return await makeApiCall(
      endpoint: '/orders/$orderId',
      method: 'GET',
    );
  }

  // Wallet Management APIs
  static Future<Map<String, dynamic>> updateWallet(int userId, double amount, String type, {String? description}) async {
    return await makeApiCall(
      endpoint: '/wallet/update',
      method: 'POST',
      body: {
        'user_id': userId,
        'amount': amount,
        'type': type,
        if (description != null) 'description': description,
      },
    );
  }

  static Future<Map<String, dynamic>> updateCommonWallet(List<int> userIds, double amount, String type, {String? description}) async {
    return await makeApiCall(
      endpoint: '/wallet/update-common',
      method: 'POST',
      body: {
        'user_ids': userIds,
        'amount': amount,
        'type': type,
        if (description != null) 'description': description,
      },
    );
  }
}
