import 'dart:async';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../constants.dart';

class DioApiService {
  static const String _tokenKey = 'auth_token';
  static const String _userIdKey = 'user_id';
  static late Dio _dio;

  // Initialize Dio with proper configuration to match curl's behavior
  static void initializeDio() {
    _dio = Dio();

    // Configure timeouts
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
    _dio.options.sendTimeout = const Duration(seconds: 30);

    // Configure headers to exactly match curl and avoid server filtering
    _dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'curl/8.7.1', // Critical: Match curl's User-Agent exactly
      'Accept-Encoding': 'gzip, deflate, br', // Match curl's encoding
      'Connection': 'keep-alive', // Match curl's connection behavior
      'Cache-Control': 'no-cache', // Prevent caching issues
    };

    // Configure HTTP client adapter with comprehensive TLS settings to match curl
    (_dio.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () {
      final client = HttpClient();

      // Connection timeouts
      client.connectionTimeout = const Duration(seconds: 30);
      client.idleTimeout = const Duration(seconds: 30);

      // Critical: Configure SSL/TLS to match curl's successful handshake
      client.badCertificateCallback = (cert, host, port) {
        print('DIO SSL: Accepting certificate for $host:$port');
        print('DIO SSL: Subject: ${cert.subject}');
        print('DIO SSL: Issuer: ${cert.issuer}');
        return true; // Accept certificate (server uses valid ZeroSSL cert)
      };

      return client;
    };

    // Add interceptor for debugging
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      error: true,
      logPrint: (object) => print('DIO: $object'),
    ));

    // Add interceptor for error handling
    _dio.interceptors.add(InterceptorsWrapper(
      onError: (error, handler) {
        print('DIO Error: ${error.message}');
        print('DIO Error Type: ${error.type}');
        if (error.response != null) {
          print('DIO Response Status: ${error.response?.statusCode}');
          print('DIO Response Data: ${error.response?.data}');
        }
        handler.next(error);
      },
    ));
  }

  // Get stored authentication token
  static Future<String?> getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }

  // Store authentication token
  static Future<void> setAuthToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, token);
  }

  // Store user ID
  static Future<void> setUserId(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userIdKey, userId);
  }

  // Get stored user ID
  static Future<String?> getUserId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userIdKey);
  }

  // Clear authentication data
  static Future<void> clearAuthData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
    await prefs.remove(_userIdKey);
  }

  // Get headers with authentication
  static Future<Map<String, String>> getHeaders() async {
    final token = await getAuthToken();
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  // Generic API call method using Dio
  static Future<Map<String, dynamic>> makeApiCall({
    required String endpoint,
    required String method,
    Map<String, dynamic>? body,
    bool requiresAuth = true,
  }) async {
    try {
      // Initialize Dio if not already done
      if (!_isDioInitialized()) {
        initializeDio();
      }

      // Fix URL construction to avoid double slashes
      String cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl.substring(0, baseUrl.length - 1) : baseUrl;
      String cleanEndpoint = endpoint.startsWith('/') ? endpoint : '/$endpoint';
      final url = '$cleanBaseUrl$cleanEndpoint';

      print('DIO API Call: $url');
      final headers = await getHeaders();
      print('DIO Headers: $headers');

      // Update headers in dio instance
      _dio.options.headers.addAll(headers);

      Response response;

      switch (method.toUpperCase()) {
        case 'GET':
          response = await _dio.get(url);
          break;
        case 'POST':
          response = await _dio.post(url, data: body);
          break;
        case 'PUT':
          response = await _dio.put(url, data: body);
          break;
        case 'DELETE':
          response = await _dio.delete(url);
          break;
        default:
          throw Exception('Unsupported HTTP method: $method');
      }

      print('DIO Response Status: ${response.statusCode}');
      print('DIO Response Data: ${response.data}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
          'statusCode': response.statusCode,
        };
      } else {
        return {
          'success': false,
          'error': response.data['message'] ?? 'Unknown error occurred',
          'statusCode': response.statusCode,
        };
      }
    } on DioException catch (e) {
      print('DIO Exception: ${e.message}');
      print('DIO Exception Type: ${e.type}');

      String errorMessage = 'Network error: ${e.message}';

      switch (e.type) {
        case DioExceptionType.connectionTimeout:
          errorMessage = 'Connection timeout. Please check your internet connection.';
          break;
        case DioExceptionType.sendTimeout:
          errorMessage = 'Send timeout. Please try again.';
          break;
        case DioExceptionType.receiveTimeout:
          errorMessage = 'Receive timeout. Please try again.';
          break;
        case DioExceptionType.connectionError:
          errorMessage = 'Connection error. Please check your internet connection.';
          if (e.message?.contains('Connection reset by peer') == true) {
            errorMessage = 'Connection was reset by the server. Please try again.';
          }
          break;
        case DioExceptionType.badResponse:
          errorMessage = 'Server error: ${e.response?.statusCode}';
          break;
        default:
          errorMessage = 'Network error: ${e.message}';
      }

      return {
        'success': false,
        'error': errorMessage,
        'statusCode': e.response?.statusCode ?? 0,
      };
    } catch (e) {
      print('DIO Unexpected Error: $e');
      return {
        'success': false,
        'error': 'Unexpected error: $e',
        'statusCode': 0,
      };
    }
  }

  static bool _isDioInitialized() {
    try {
      return _dio.options.connectTimeout != null;
    } catch (e) {
      return false;
    }
  }

  // Admin Authentication APIs
  static Future<Map<String, dynamic>> adminLogin(String email, String password, {String? fcmToken}) async {
    return await makeApiCall(
      endpoint: '/admin/login',
      method: 'POST',
      body: {
        'email': email,
        'password': password,
        if (fcmToken != null) 'fcm_token': fcmToken,
      },
      requiresAuth: false,
    );
  }

  static Future<Map<String, dynamic>> validateAdmin() async {
    return await makeApiCall(
      endpoint: '/admin/validate',
      method: 'GET',
    );
  }
}
