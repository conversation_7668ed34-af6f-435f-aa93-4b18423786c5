import 'dart:io';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';

Future<void> main() async {
  print('🔍 Testing SSL/TLS Configuration to Match Curl');
  print('===============================================');
  
  await testWithEnhancedSSLConfig();
  await testWithRelaxedSSLConfig();
  await testWithStrictSSLConfig();
}

Future<void> testWithEnhancedSSLConfig() async {
  print('\n📱 TEST 1: Enhanced SSL/TLS Configuration');
  print('=========================================');
  
  try {
    final dio = Dio();
    
    // Configure timeouts
    dio.options.connectTimeout = const Duration(seconds: 30);
    dio.options.receiveTimeout = const Duration(seconds: 30);
    dio.options.sendTimeout = const Duration(seconds: 30);
    
    // Configure headers to exactly match curl
    dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'curl/8.7.1',
      'Host': 'zoomfresh.co.in',
    };

    // Enhanced HTTP client configuration
    (dio.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () {
      final client = HttpClient();
      
      // Connection settings
      client.connectionTimeout = const Duration(seconds: 30);
      client.idleTimeout = const Duration(seconds: 30);
      
      // SSL/TLS Configuration to match curl's TLS 1.3 behavior
      client.badCertificateCallback = (cert, host, port) {
        print('   🔒 SSL Certificate Details:');
        print('      Host: $host:$port');
        print('      Subject: ${cert.subject}');
        print('      Issuer: ${cert.issuer}');
        print('      Valid from: ${cert.startValidity}');
        print('      Valid to: ${cert.endValidity}');
        return true; // Accept certificate
      };
      
      return client;
    };

    print('   🔄 Making API call with enhanced SSL config...');
    
    final response = await dio.post(
      'https://zoomfresh.co.in/api/admin/login',
      data: {
        'email': '<EMAIL>',
        'password': 'password123',
        'fcm_token': 'test_token',
      },
    );

    print('   ✅ Status Code: ${response.statusCode}');
    if (response.statusCode == 200) {
      print('   🎉 SUCCESS: Enhanced SSL config works!');
    }
  } catch (e) {
    print('   ❌ ERROR: $e');
  }
}

Future<void> testWithRelaxedSSLConfig() async {
  print('\n📱 TEST 2: Relaxed SSL Configuration');
  print('====================================');
  
  try {
    final dio = Dio();
    
    dio.options.connectTimeout = const Duration(seconds: 30);
    dio.options.receiveTimeout = const Duration(seconds: 30);
    dio.options.sendTimeout = const Duration(seconds: 30);
    
    dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'curl/8.7.1',
    };

    // Very relaxed SSL configuration
    (dio.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () {
      final client = HttpClient();
      
      // Disable SSL verification completely for testing
      client.badCertificateCallback = (cert, host, port) => true;
      
      // Relaxed connection settings
      client.connectionTimeout = const Duration(seconds: 60);
      client.idleTimeout = const Duration(seconds: 60);
      
      return client;
    };

    print('   🔄 Making API call with relaxed SSL config...');
    
    final response = await dio.post(
      'https://zoomfresh.co.in/api/admin/login',
      data: {
        'email': '<EMAIL>',
        'password': 'password123',
        'fcm_token': 'test_token',
      },
    );

    print('   ✅ Status Code: ${response.statusCode}');
    if (response.statusCode == 200) {
      print('   🎉 SUCCESS: Relaxed SSL config works!');
    }
  } catch (e) {
    print('   ❌ ERROR: $e');
  }
}

Future<void> testWithStrictSSLConfig() async {
  print('\n📱 TEST 3: Strict SSL Configuration (Default)');
  print('==============================================');
  
  try {
    final dio = Dio();
    
    dio.options.connectTimeout = const Duration(seconds: 30);
    dio.options.receiveTimeout = const Duration(seconds: 30);
    dio.options.sendTimeout = const Duration(seconds: 30);
    
    dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'curl/8.7.1',
    };

    // Use default SSL configuration (strict)
    print('   🔄 Making API call with default SSL config...');
    
    final response = await dio.post(
      'https://zoomfresh.co.in/api/admin/login',
      data: {
        'email': '<EMAIL>',
        'password': 'password123',
        'fcm_token': 'test_token',
      },
    );

    print('   ✅ Status Code: ${response.statusCode}');
    if (response.statusCode == 200) {
      print('   🎉 SUCCESS: Default SSL config works!');
    }
  } catch (e) {
    print('   ❌ ERROR: $e');
  }
}
