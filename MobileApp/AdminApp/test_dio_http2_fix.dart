import 'dart:convert';
import 'package:http/http.dart' as http;
import 'lib/services/dio_api_service.dart';

Future<void> main() async {
  print('🔍 Testing Dio HTTP/2 Fix for ZoomFresh Admin API');
  print('==================================================');
  
  // Initialize Dio with HTTP/2 configuration
  DioApiService.initializeDio();
  
  await testWithDioHttp2();
  await testWithStandardHttp();
}

Future<void> testWithDioHttp2() async {
  print('\n📱 TEST 1: Dio with HTTP/2 Configuration');
  print('=========================================');
  
  try {
    final result = await DioApiService.adminLogin(
      '<EMAIL>',
      'password123',
      fcmToken: 'test_token',
    );

    print('   ✅ Success: ${result['success']}');
    print('   📊 Status Code: ${result['statusCode']}');
    
    if (result['success']) {
      print('   🎉 SUCCESS: Dio HTTP/2 fix works!');
      print('   📄 Response: ${result['data'].toString().substring(0, 100)}...');
    } else {
      print('   ❌ FAILED: ${result['error']}');
    }
  } catch (e) {
    print('   ❌ ERROR: $e');
  }
}

Future<void> testWithStandardHttp() async {
  print('\n📱 TEST 2: Standard HTTP Package (HTTP/1.1)');
  print('============================================');
  
  try {
    final url = Uri.parse('https://zoomfresh.co.in/api/admin/login');
    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'curl/8.7.1',
      },
      body: jsonEncode({
        'email': '<EMAIL>',
        'password': 'password123',
        'fcm_token': 'test_token',
      }),
    ).timeout(Duration(seconds: 30));

    print('   ✅ Status Code: ${response.statusCode}');
    
    if (response.statusCode == 200) {
      print('   🎉 SUCCESS: Standard HTTP works!');
      print('   📄 Response: ${response.body.substring(0, 100)}...');
    } else {
      print('   ⚠️  Unexpected status: ${response.statusCode}');
    }
  } catch (e) {
    print('   ❌ ERROR: $e');
  }
}
