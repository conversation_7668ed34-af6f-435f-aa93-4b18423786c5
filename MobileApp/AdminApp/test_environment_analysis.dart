import 'dart:io';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';

Future<void> main() async {
  print('🔍 Environment Analysis: Local vs Production');
  print('============================================');
  
  await testLocalEnvironment();
  await testWithDifferentPorts();
  await testWithCustomDNS();
  await analyzeNetworkPath();
}

Future<void> testLocalEnvironment() async {
  print('\n📱 TEST 1: Local Development Environment');
  print('=======================================');
  
  try {
    print('   🔄 Testing local Laravel server (if running)...');
    
    final dio = Dio();
    dio.options.connectTimeout = const Duration(seconds: 10);
    dio.options.receiveTimeout = const Duration(seconds: 10);
    
    dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'curl/8.7.1',
    };

    // Test local environment first
    try {
      final localResponse = await dio.post(
        'http://localhost:8000/api/admin/login',
        data: {
          'email': '<EMAIL>',
          'password': 'password123',
          'fcm_token': 'test_token',
        },
      );
      
      print('   ✅ Local environment works! Status: ${localResponse.statusCode}');
      print('   📄 This confirms the issue is production-specific');
    } catch (e) {
      print('   ⚠️  Local environment not available: ${e.toString().substring(0, 50)}...');
    }
    
  } catch (e) {
    print('   ❌ Local test error: $e');
  }
}

Future<void> testWithDifferentPorts() async {
  print('\n📱 TEST 2: Different Port and Protocol Testing');
  print('==============================================');
  
  final testUrls = [
    'https://zoomfresh.co.in:443/api/admin/login',
    'http://zoomfresh.co.in:80/api/admin/login',
    'https://*************/api/admin/login', // Direct IP
    'http://*************/api/admin/login',  // Direct IP HTTP
  ];
  
  for (final url in testUrls) {
    print('\n   🔄 Testing: $url');
    
    try {
      final dio = Dio();
      dio.options.connectTimeout = const Duration(seconds: 15);
      dio.options.receiveTimeout = const Duration(seconds: 15);
      
      dio.options.headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'curl/8.7.1',
        'Host': 'zoomfresh.co.in', // Keep original host even for IP
      };

      final response = await dio.post(
        url,
        data: {
          'email': '<EMAIL>',
          'password': 'password123',
          'fcm_token': 'test_token',
        },
      );

      print('      ✅ SUCCESS: $url - Status: ${response.statusCode}');
      return; // If one works, we found a solution
    } catch (e) {
      print('      ❌ FAILED: $url - ${e.toString().substring(0, 60)}...');
    }
  }
}

Future<void> testWithCustomDNS() async {
  print('\n📱 TEST 3: DNS and Network Path Analysis');
  print('========================================');
  
  try {
    print('   🔄 Checking DNS resolution...');
    
    final addresses = await InternetAddress.lookup('zoomfresh.co.in');
    print('   📊 DNS Resolution:');
    for (final addr in addresses) {
      print('      - ${addr.address} (${addr.type})');
    }
    
    print('\n   🔄 Testing direct socket connection...');
    
    final socket = await Socket.connect('zoomfresh.co.in', 443, timeout: Duration(seconds: 10));
    print('   ✅ Socket connection successful');
    print('   📊 Connected to: ${socket.remoteAddress}:${socket.remotePort}');
    socket.destroy();
    
  } catch (e) {
    print('   ❌ DNS/Socket test failed: $e');
  }
}

Future<void> analyzeNetworkPath() async {
  print('\n📱 TEST 4: Network Path and Hosting Analysis');
  print('============================================');
  
  try {
    print('   🔄 Analyzing hosting environment...');
    
    // Test basic HTTP connectivity
    final result = await Process.run('curl', [
      '-I', // Head request only
      'https://zoomfresh.co.in',
      '--max-time', '10',
      '--user-agent', 'curl/8.7.1',
    ]);
    
    if (result.exitCode == 0) {
      print('   ✅ Curl HEAD request successful');
      final headers = result.stdout.toString();
      
      if (headers.contains('server:')) {
        final serverLine = headers.split('\n').firstWhere(
          (line) => line.toLowerCase().contains('server:'),
          orElse: () => 'Server: Unknown',
        );
        print('   📊 $serverLine');
      }
      
      if (headers.contains('cloudflare') || headers.contains('cf-')) {
        print('   🛡️  Cloudflare detected - This might be the filtering source!');
      }
      
      if (headers.contains('hostinger') || headers.contains('litespeed')) {
        print('   🏢 Hostinger/LiteSpeed detected');
      }
      
    } else {
      print('   ❌ Curl HEAD request failed: ${result.stderr}');
    }
    
  } catch (e) {
    print('   ❌ Network analysis failed: $e');
  }
}

// Additional diagnostic function
Future<void> generateDiagnosticReport() async {
  print('\n📋 DIAGNOSTIC REPORT');
  print('===================');
  
  print('🔍 Issue Summary:');
  print('   - Curl works perfectly (HTTP/2, TLS 1.3)');
  print('   - All Dart/Flutter HTTP clients fail with "Connection reset by peer"');
  print('   - Failure occurs at TLS handshake level, not HTTP level');
  print('   - Issue affects both emulator and physical devices');
  
  print('\n🎯 Likely Causes:');
  print('   1. Hosting provider (Hostinger) DDoS protection');
  print('   2. Advanced client fingerprinting beyond User-Agent');
  print('   3. TLS fingerprinting detecting non-browser clients');
  print('   4. CDN/Proxy filtering (Cloudflare, etc.)');
  print('   5. Server-side security policies blocking Dart HTTP clients');
  
  print('\n💡 Potential Solutions:');
  print('   1. Contact hosting provider to whitelist Flutter/Dart clients');
  print('   2. Use a proxy service to relay requests');
  print('   3. Implement server-side API proxy endpoint');
  print('   4. Configure hosting security to allow mobile app clients');
  print('   5. Use alternative HTTP client libraries');
}
