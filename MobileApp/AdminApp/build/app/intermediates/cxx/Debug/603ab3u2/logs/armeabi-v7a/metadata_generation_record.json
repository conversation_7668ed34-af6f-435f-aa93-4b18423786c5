[{"level_": 0, "message_": "Start JSON generation. Platform version: 23 min SDK version: armeabi-v7a", "file_": "/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON '/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/.cxx/Debug/603ab3u2/armeabi-v7a/android_gradle_build.json' was up-to-date", "file_": "/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]