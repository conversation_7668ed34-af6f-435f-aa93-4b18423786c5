1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.user"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:2:1-63
15-->/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:2:18-60
16    <uses-permission android:name="android.permission.CAMERA" />
16-->/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:3:1-61
16-->/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:3:18-58
17
18    <uses-feature android:name="android.hardware.camera" />
18-->/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:4:1-56
18-->/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:4:15-53
19    <uses-feature android:name="android.hardware.camera.autofocus" />
19-->/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:5:1-66
19-->/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:5:15-63
20
21    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
21-->/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:6:1-77
21-->/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:6:18-74
22    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
22-->/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:7:1-76
22-->/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:7:18-73
23    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
23-->/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:8:1-76
23-->/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:8:18-74
24    <uses-permission android:name="android.permission.RECEIVE" />
24-->/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:9:1-61
24-->/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:9:18-59
25    <uses-permission android:name="android.permission.WAKE_LOCK" />
25-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-68
25-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:22-65
26    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Permissions options for the `notification` group -->
26-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:5-79
26-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:22-76
27    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
27-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:5-77
27-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:22-74
28    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
28-->[com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:26:5-82
28-->[com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:26:22-79
29    <uses-permission android:name="android.permission.VIBRATE" />
29-->[:flutter_local_notifications] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-66
29-->[:flutter_local_notifications] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:22-63
30
31    <permission
31-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/b070f665392501a799c8a6c90c36edfd/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
32        android:name="com.example.user.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
32-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/b070f665392501a799c8a6c90c36edfd/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
33        android:protectionLevel="signature" />
33-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/b070f665392501a799c8a6c90c36edfd/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
34
35    <uses-permission android:name="com.example.user.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
35-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/b070f665392501a799c8a6c90c36edfd/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
35-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/b070f665392501a799c8a6c90c36edfd/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
36
37    <application
38        android:name="android.app.Application"
39        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
39-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/b070f665392501a799c8a6c90c36edfd/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
40        android:debuggable="true"
41        android:extractNativeLibs="false"
42        android:icon="@mipmap/ic_launcher"
43        android:label="ZoomFresh Admin"
44        android:networkSecurityConfig="@xml/network_security_config"
45        android:usesCleartextTraffic="true" >
46        <activity
47            android:name="com.example.user.MainActivity"
48            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
49            android:exported="true"
50            android:hardwareAccelerated="true"
51            android:launchMode="singleTop"
52            android:theme="@style/LaunchTheme"
53            android:windowSoftInputMode="adjustResize" >
54
55            <!--
56                 Specifies an Android theme to apply to this Activity as soon as
57                 the Android process has started. This theme is visible to the user
58                 while the Flutter UI initializes. After that, this theme continues
59                 to determine the Window background behind the Flutter UI.
60            -->
61            <meta-data
62                android:name="io.flutter.embedding.android.NormalTheme"
63                android:resource="@style/NormalTheme" />
64
65            <intent-filter>
66                <action android:name="android.intent.action.MAIN" />
67
68                <category android:name="android.intent.category.LAUNCHER" />
69            </intent-filter>
70        </activity>
71        <!--
72             Don't delete the meta-data below.
73             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
74        -->
75        <meta-data
76            android:name="flutterEmbedding"
77            android:value="2" />
78        <!--
79           Declares a provider which allows us to store files to share in
80           '.../caches/share_plus' and grant the receiving action access
81        -->
82        <provider
82-->[:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:9-21:20
83            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
83-->[:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-77
84            android:authorities="com.example.user.flutter.share_provider"
84-->[:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-74
85            android:exported="false"
85-->[:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-37
86            android:grantUriPermissions="true" >
86-->[:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:13-47
87            <meta-data
87-->[:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:13-20:68
88                android:name="android.support.FILE_PROVIDER_PATHS"
88-->[:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:17-67
89                android:resource="@xml/flutter_share_file_paths" />
89-->[:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:17-65
90        </provider>
91        <!--
92           This manifest declared broadcast receiver allows us to use an explicit
93           Intent when creating a PendingItent to be informed of the user's choice
94        -->
95        <receiver
95-->[:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:9-32:20
96            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
96-->[:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:13-82
97            android:exported="false" >
97-->[:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:13-37
98            <intent-filter>
98-->[:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:13-31:29
99                <action android:name="EXTRA_CHOSEN_COMPONENT" />
99-->[:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:17-65
99-->[:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:25-62
100            </intent-filter>
101        </receiver>
102
103        <service
103-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:9-17:72
104            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
104-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-107
105            android:exported="false"
105-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-37
106            android:permission="android.permission.BIND_JOB_SERVICE" />
106-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:13-69
107        <service
107-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:9-24:19
108            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
108-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:13-97
109            android:exported="false" >
109-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:13-37
110            <intent-filter>
110-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:13-23:29
111                <action android:name="com.google.firebase.MESSAGING_EVENT" />
111-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:17-78
111-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:25-75
112            </intent-filter>
113        </service>
114
115        <receiver
115-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:9-33:20
116            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
116-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:13-98
117            android:exported="true"
117-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:13-36
118            android:permission="com.google.android.c2dm.permission.SEND" >
118-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:13-73
119            <intent-filter>
119-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:13-32:29
120                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
120-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:31:17-81
120-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:31:25-78
121            </intent-filter>
122        </receiver>
123
124        <service
124-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:35:9-39:19
125            android:name="com.google.firebase.components.ComponentDiscoveryService"
125-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:35:18-89
126            android:directBootAware="true"
126-->[com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:32:13-43
127            android:exported="false" >
127-->[com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:56:13-37
128            <meta-data
128-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:36:13-38:85
129                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
129-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:37:17-128
130                android:value="com.google.firebase.components.ComponentRegistrar" />
130-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:38:17-82
131            <meta-data
131-->[:firebase_core] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-11:85
132                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
132-->[:firebase_core] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:17-124
133                android:value="com.google.firebase.components.ComponentRegistrar" />
133-->[:firebase_core] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:17-82
134            <meta-data
134-->[com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:57:13-59:85
135                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
135-->[com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:58:17-122
136                android:value="com.google.firebase.components.ComponentRegistrar" />
136-->[com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:59:17-82
137            <meta-data
137-->[com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:60:13-62:85
138                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
138-->[com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:61:17-119
139                android:value="com.google.firebase.components.ComponentRegistrar" />
139-->[com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:62:17-82
140            <meta-data
140-->[com.google.firebase:firebase-installations:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8c5f854e79dc68a2edf523fe4f0e5ee9/transformed/jetified-firebase-installations-19.0.0/AndroidManifest.xml:15:13-17:85
141                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
141-->[com.google.firebase:firebase-installations:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8c5f854e79dc68a2edf523fe4f0e5ee9/transformed/jetified-firebase-installations-19.0.0/AndroidManifest.xml:16:17-130
142                android:value="com.google.firebase.components.ComponentRegistrar" />
142-->[com.google.firebase:firebase-installations:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8c5f854e79dc68a2edf523fe4f0e5ee9/transformed/jetified-firebase-installations-19.0.0/AndroidManifest.xml:17:17-82
143            <meta-data
143-->[com.google.firebase:firebase-installations:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8c5f854e79dc68a2edf523fe4f0e5ee9/transformed/jetified-firebase-installations-19.0.0/AndroidManifest.xml:18:13-20:85
144                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
144-->[com.google.firebase:firebase-installations:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8c5f854e79dc68a2edf523fe4f0e5ee9/transformed/jetified-firebase-installations-19.0.0/AndroidManifest.xml:19:17-127
145                android:value="com.google.firebase.components.ComponentRegistrar" />
145-->[com.google.firebase:firebase-installations:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8c5f854e79dc68a2edf523fe4f0e5ee9/transformed/jetified-firebase-installations-19.0.0/AndroidManifest.xml:20:17-82
146            <meta-data
146-->[com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:35:13-37:85
147                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
147-->[com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:36:17-109
148                android:value="com.google.firebase.components.ComponentRegistrar" />
148-->[com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:37:17-82
149            <meta-data
149-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/88e7bcef79866b370f51d90278a6d617/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:25:13-27:85
150                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
150-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/88e7bcef79866b370f51d90278a6d617/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:26:17-115
151                android:value="com.google.firebase.components.ComponentRegistrar" />
151-->[com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/88e7bcef79866b370f51d90278a6d617/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:27:17-82
152        </service>
153
154        <provider
154-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:41:9-45:38
155            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
155-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:42:13-102
156            android:authorities="com.example.user.flutterfirebasemessaginginitprovider"
156-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:43:13-88
157            android:exported="false"
157-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:44:13-37
158            android:initOrder="99" />
158-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:45:13-35
159
160        <receiver
160-->[com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:29:9-40:20
161            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
161-->[com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:30:13-78
162            android:exported="true"
162-->[com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:31:13-36
163            android:permission="com.google.android.c2dm.permission.SEND" >
163-->[com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:32:13-73
164            <intent-filter>
164-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:13-32:29
165                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
165-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:31:17-81
165-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:31:25-78
166            </intent-filter>
167
168            <meta-data
168-->[com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:37:13-39:40
169                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
169-->[com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:38:17-92
170                android:value="true" />
170-->[com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:39:17-37
171        </receiver>
172        <!--
173             FirebaseMessagingService performs security checks at runtime,
174             but set to not exported to explicitly avoid allowing another app to call it.
175        -->
176        <service
176-->[com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:46:9-53:19
177            android:name="com.google.firebase.messaging.FirebaseMessagingService"
177-->[com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:47:13-82
178            android:directBootAware="true"
178-->[com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:48:13-43
179            android:exported="false" >
179-->[com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:49:13-37
180            <intent-filter android:priority="-500" >
180-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:13-23:29
181                <action android:name="com.google.firebase.MESSAGING_EVENT" />
181-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:17-78
181-->[:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:25-75
182            </intent-filter>
183        </service>
184
185        <provider
185-->[com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:23:9-28:39
186            android:name="com.google.firebase.provider.FirebaseInitProvider"
186-->[com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:24:13-77
187            android:authorities="com.example.user.firebaseinitprovider"
187-->[com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:25:13-72
188            android:directBootAware="true"
188-->[com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:26:13-43
189            android:exported="false"
189-->[com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:27:13-37
190            android:initOrder="100" />
190-->[com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:28:13-36
191
192        <uses-library
192-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/ce8cfb0886867a7af33a5d639c9417b6/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
193            android:name="androidx.window.extensions"
193-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/ce8cfb0886867a7af33a5d639c9417b6/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
194            android:required="false" />
194-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/ce8cfb0886867a7af33a5d639c9417b6/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
195        <uses-library
195-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/ce8cfb0886867a7af33a5d639c9417b6/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
196            android:name="androidx.window.sidecar"
196-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/ce8cfb0886867a7af33a5d639c9417b6/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
197            android:required="false" />
197-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/ce8cfb0886867a7af33a5d639c9417b6/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
198
199        <activity
199-->[com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/e5ef794f53c1b3db91c9205ef71e7cf4/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:20:9-22:45
200            android:name="com.google.android.gms.common.api.GoogleApiActivity"
200-->[com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/e5ef794f53c1b3db91c9205ef71e7cf4/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:20:19-85
201            android:exported="false"
201-->[com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/e5ef794f53c1b3db91c9205ef71e7cf4/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:22:19-43
202            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
202-->[com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/e5ef794f53c1b3db91c9205ef71e7cf4/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:21:19-78
203
204        <provider
204-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/a9e8ac145e479222ef1a07acece1be4f/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
205            android:name="androidx.startup.InitializationProvider"
205-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/a9e8ac145e479222ef1a07acece1be4f/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:25:13-67
206            android:authorities="com.example.user.androidx-startup"
206-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/a9e8ac145e479222ef1a07acece1be4f/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:26:13-68
207            android:exported="false" >
207-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/a9e8ac145e479222ef1a07acece1be4f/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:27:13-37
208            <meta-data
208-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/a9e8ac145e479222ef1a07acece1be4f/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
209                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
209-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/a9e8ac145e479222ef1a07acece1be4f/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
210                android:value="androidx.startup" />
210-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/a9e8ac145e479222ef1a07acece1be4f/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
211            <meta-data
211-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
212                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
212-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
213                android:value="androidx.startup" />
213-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
214        </provider>
215
216        <meta-data
216-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/4c3674e95d4757aa8e62cb4489c3d172/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:21:9-23:69
217            android:name="com.google.android.gms.version"
217-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/4c3674e95d4757aa8e62cb4489c3d172/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:22:13-58
218            android:value="@integer/google_play_services_version" />
218-->[com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/4c3674e95d4757aa8e62cb4489c3d172/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:23:13-66
219
220        <receiver
220-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
221            android:name="androidx.profileinstaller.ProfileInstallReceiver"
221-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
222            android:directBootAware="false"
222-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
223            android:enabled="true"
223-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
224            android:exported="true"
224-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
225            android:permission="android.permission.DUMP" >
225-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
226            <intent-filter>
226-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
227                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
227-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
227-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
228            </intent-filter>
229            <intent-filter>
229-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
230                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
230-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
230-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
231            </intent-filter>
232            <intent-filter>
232-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
233                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
233-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
233-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
234            </intent-filter>
235            <intent-filter>
235-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
236                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
236-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
236-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
237            </intent-filter>
238        </receiver>
239
240        <service
240-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/63cf836f20c0f1dc738cdf6d06cef59d/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:28:9-34:19
241            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
241-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/63cf836f20c0f1dc738cdf6d06cef59d/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:29:13-103
242            android:exported="false" >
242-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/63cf836f20c0f1dc738cdf6d06cef59d/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:30:13-37
243            <meta-data
243-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/63cf836f20c0f1dc738cdf6d06cef59d/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:31:13-33:39
244                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
244-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/63cf836f20c0f1dc738cdf6d06cef59d/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:32:17-94
245                android:value="cct" />
245-->[com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/63cf836f20c0f1dc738cdf6d06cef59d/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:33:17-36
246        </service>
247        <service
247-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/6e2cc864b4294b37ed6688e81c623366/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:26:9-30:19
248            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
248-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/6e2cc864b4294b37ed6688e81c623366/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:27:13-117
249            android:exported="false"
249-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/6e2cc864b4294b37ed6688e81c623366/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:28:13-37
250            android:permission="android.permission.BIND_JOB_SERVICE" >
250-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/6e2cc864b4294b37ed6688e81c623366/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:29:13-69
251        </service>
252
253        <receiver
253-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/6e2cc864b4294b37ed6688e81c623366/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:32:9-34:40
254            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
254-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/6e2cc864b4294b37ed6688e81c623366/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:33:13-132
255            android:exported="false" />
255-->[com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/6e2cc864b4294b37ed6688e81c623366/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:34:13-37
256    </application>
257
258</manifest>
