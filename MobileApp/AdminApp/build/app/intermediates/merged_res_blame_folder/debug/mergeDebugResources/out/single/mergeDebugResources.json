[{"merged": "com.example.user.app-debug-48:/mipmap-xxhdpi_ic_launcher.png.flat", "source": "com.example.user.app-main-42:/mipmap-xxhdpi/ic_launcher.png"}, {"merged": "com.example.user.app-debug-48:/mipmap-hdpi_ic_launcher.png.flat", "source": "com.example.user.app-main-42:/mipmap-hdpi/ic_launcher.png"}, {"merged": "com.example.user.app-debug-48:/drawable-v21_launch_background.xml.flat", "source": "com.example.user.app-main-42:/drawable-v21/launch_background.xml"}, {"merged": "com.example.user.app-debug-48:/mipmap-xhdpi_ic_launcher.png.flat", "source": "com.example.user.app-main-42:/mipmap-xhdpi/ic_launcher.png"}, {"merged": "com.example.user.app-debug-48:/raw_custom_sound.wav.flat", "source": "com.example.user.app-main-42:/raw/custom_sound.wav"}, {"merged": "com.example.user.app-debug-48:/mipmap-xxxhdpi_ic_launcher.png.flat", "source": "com.example.user.app-main-42:/mipmap-xxxhdpi/ic_launcher.png"}, {"merged": "com.example.user.app-debug-48:/mipmap-mdpi_ic_launcher.png.flat", "source": "com.example.user.app-main-42:/mipmap-mdpi/ic_launcher.png"}, {"merged": "com.example.user.app-debug-48:/xml_network_security_config.xml.flat", "source": "com.example.user.app-main-42:/xml/network_security_config.xml"}]