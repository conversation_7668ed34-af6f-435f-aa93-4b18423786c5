com.example.user.app-jetified-savedstate-ktx-1.2.1-0 /Users/<USER>/.gradle/caches/8.12/transforms/0f9299326b12e3b7699977d626199805/transformed/jetified-savedstate-ktx-1.2.1/res
com.example.user.app-coordinatorlayout-1.0.0-1 /Users/<USER>/.gradle/caches/8.12/transforms/1091c4e43d4771808b3a5141d1c246ad/transformed/coordinatorlayout-1.0.0/res
com.example.user.app-jetified-core-ktx-1.13.1-2 /Users/<USER>/.gradle/caches/8.12/transforms/16ddbb077446d9c6f09bab560a572eaa/transformed/jetified-core-ktx-1.13.1/res
com.example.user.app-jetified-window-java-1.2.0-3 /Users/<USER>/.gradle/caches/8.12/transforms/18f4262c3eeb6167bdf47eac4f8e6e1f/transformed/jetified-window-java-1.2.0/res
com.example.user.app-localbroadcastmanager-1.1.0-4 /Users/<USER>/.gradle/caches/8.12/transforms/2f9c75fb3ff07661e4d00855f2f6b792/transformed/localbroadcastmanager-1.1.0/res
com.example.user.app-jetified-activity-1.8.1-5 /Users/<USER>/.gradle/caches/8.12/transforms/33e07ea9350fd22c13f24f7a58c5ffc5/transformed/jetified-activity-1.8.1/res
com.example.user.app-jetified-lifecycle-viewmodel-ktx-2.7.0-6 /Users/<USER>/.gradle/caches/8.12/transforms/362c748c2afeef6f9542689b59ea464d/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/res
com.example.user.app-lifecycle-livedata-core-2.7.0-7 /Users/<USER>/.gradle/caches/8.12/transforms/3f3461b291f3734e4292897ea57fd72f/transformed/lifecycle-livedata-core-2.7.0/res
com.example.user.app-lifecycle-runtime-2.7.0-8 /Users/<USER>/.gradle/caches/8.12/transforms/438b84687865a785ac5acced4d35c112/transformed/lifecycle-runtime-2.7.0/res
com.example.user.app-jetified-fragment-ktx-1.7.1-9 /Users/<USER>/.gradle/caches/8.12/transforms/48a501c37d3905e479a6a9a1e584fdbb/transformed/jetified-fragment-ktx-1.7.1/res
com.example.user.app-jetified-play-services-basement-18.3.0-10 /Users/<USER>/.gradle/caches/8.12/transforms/4c3674e95d4757aa8e62cb4489c3d172/transformed/jetified-play-services-basement-18.3.0/res
com.example.user.app-jetified-core-1.0.0-11 /Users/<USER>/.gradle/caches/8.12/transforms/4ddb0b5c244f312bc4e2894477ecd4fe/transformed/jetified-core-1.0.0/res
com.example.user.app-appcompat-1.1.0-12 /Users/<USER>/.gradle/caches/8.12/transforms/4e897a92a8589081ccc4806d7917ee6d/transformed/appcompat-1.1.0/res
com.example.user.app-fragment-1.7.1-13 /Users/<USER>/.gradle/caches/8.12/transforms/611301238bcdcb1e86c512269ca104a3/transformed/fragment-1.7.1/res
com.example.user.app-jetified-firebase-common-22.0.0-14 /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/res
com.example.user.app-jetified-profileinstaller-1.3.1-15 /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/res
com.example.user.app-jetified-firebase-messaging-25.0.0-16 /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/res
com.example.user.app-slidingpanelayout-1.2.0-17 /Users/<USER>/.gradle/caches/8.12/transforms/7df2305af7def53c0baebd27c553faa7/transformed/slidingpanelayout-1.2.0/res
com.example.user.app-media-1.1.0-18 /Users/<USER>/.gradle/caches/8.12/transforms/8835dc625064015190b6c0ed66042883/transformed/media-1.1.0/res
com.example.user.app-lifecycle-viewmodel-2.7.0-19 /Users/<USER>/.gradle/caches/8.12/transforms/9e4c707523669c03674d0b6e76e75c9e/transformed/lifecycle-viewmodel-2.7.0/res
com.example.user.app-jetified-lifecycle-process-2.7.0-20 /Users/<USER>/.gradle/caches/8.12/transforms/a9e8ac145e479222ef1a07acece1be4f/transformed/jetified-lifecycle-process-2.7.0/res
com.example.user.app-preference-1.2.1-21 /Users/<USER>/.gradle/caches/8.12/transforms/ab33acf9f8491d391c4b50ceda6aad27/transformed/preference-1.2.1/res
com.example.user.app-core-1.13.1-22 /Users/<USER>/.gradle/caches/8.12/transforms/b070f665392501a799c8a6c90c36edfd/transformed/core-1.13.1/res
com.example.user.app-jetified-activity-ktx-1.8.1-23 /Users/<USER>/.gradle/caches/8.12/transforms/b3cb6375898834fd8b20f57194767be8/transformed/jetified-activity-ktx-1.8.1/res
com.example.user.app-jetified-datastore-preferences-release-24 /Users/<USER>/.gradle/caches/8.12/transforms/b74446f24719d70707fefbec350004a6/transformed/jetified-datastore-preferences-release/res
com.example.user.app-jetified-savedstate-1.2.1-25 /Users/<USER>/.gradle/caches/8.12/transforms/ba7ca02849579a5942bc5e00dce7ee34/transformed/jetified-savedstate-1.2.1/res
com.example.user.app-transition-1.4.1-26 /Users/<USER>/.gradle/caches/8.12/transforms/bbc9215d2fdfe6f42ae8913f3f5289ad/transformed/transition-1.4.1/res
com.example.user.app-jetified-window-1.2.0-27 /Users/<USER>/.gradle/caches/8.12/transforms/ce8cfb0886867a7af33a5d639c9417b6/transformed/jetified-window-1.2.0/res
com.example.user.app-jetified-startup-runtime-1.1.1-28 /Users/<USER>/.gradle/caches/8.12/transforms/cf8e7641e0d34884d0fa18ff00d5bde6/transformed/jetified-startup-runtime-1.1.1/res
com.example.user.app-recyclerview-1.0.0-29 /Users/<USER>/.gradle/caches/8.12/transforms/d8b6869b89e3031c57ed06b384b79e6e/transformed/recyclerview-1.0.0/res
com.example.user.app-jetified-datastore-release-30 /Users/<USER>/.gradle/caches/8.12/transforms/da19215eac54a2bea1d6bc195902ef1f/transformed/jetified-datastore-release/res
com.example.user.app-core-runtime-2.2.0-31 /Users/<USER>/.gradle/caches/8.12/transforms/dbccd487fa973b76ae556af0c8ed0b74/transformed/core-runtime-2.2.0/res
com.example.user.app-jetified-datastore-core-release-32 /Users/<USER>/.gradle/caches/8.12/transforms/e507f6d5eb5275dda970c8593cfa2c86/transformed/jetified-datastore-core-release/res
com.example.user.app-lifecycle-livedata-2.7.0-33 /Users/<USER>/.gradle/caches/8.12/transforms/e5b6ea368209bc8c41b2c6ae4fa7b464/transformed/lifecycle-livedata-2.7.0/res
com.example.user.app-jetified-lifecycle-runtime-ktx-2.7.0-34 /Users/<USER>/.gradle/caches/8.12/transforms/e5ba0e2d74aec3d8c1bca048cd0c2b9f/transformed/jetified-lifecycle-runtime-ktx-2.7.0/res
com.example.user.app-jetified-play-services-base-18.1.0-35 /Users/<USER>/.gradle/caches/8.12/transforms/e5ef794f53c1b3db91c9205ef71e7cf4/transformed/jetified-play-services-base-18.1.0/res
com.example.user.app-jetified-lifecycle-livedata-core-ktx-2.7.0-36 /Users/<USER>/.gradle/caches/8.12/transforms/e6b4f321bb42a880e130349820d2c12f/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/res
com.example.user.app-jetified-tracing-1.2.0-37 /Users/<USER>/.gradle/caches/8.12/transforms/eeb836bac5c9af717aebd341ad53ada4/transformed/jetified-tracing-1.2.0/res
com.example.user.app-jetified-annotation-experimental-1.4.0-38 /Users/<USER>/.gradle/caches/8.12/transforms/f69c4d7e62ac92eb85be6d4211abb555/transformed/jetified-annotation-experimental-1.4.0/res
com.example.user.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-39 /Users/<USER>/.gradle/caches/8.12/transforms/f6cab6af970b1956ac5a793fb687a128/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/res
com.example.user.app-jetified-appcompat-resources-1.1.0-40 /Users/<USER>/.gradle/caches/8.12/transforms/f80dfb8940b4ea060ece7c8198949a6f/transformed/jetified-appcompat-resources-1.1.0/res
com.example.user.app-debug-41 /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/debug/res
com.example.user.app-main-42 /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/res
com.example.user.app-google-services-43 /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/generated/res/google-services/debug
com.example.user.app-pngs-44 /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/generated/res/pngs/debug
com.example.user.app-resValues-45 /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/generated/res/resValues/debug
com.example.user.app-packageDebugResources-46 /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/incremental/debug/packageDebugResources/merged.dir
com.example.user.app-packageDebugResources-47 /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/incremental/debug/packageDebugResources/stripped.dir
com.example.user.app-debug-48 /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/merged_res/debug/mergeDebugResources
com.example.user.app-debug-49 /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_core/intermediates/packaged_res/debug/packageDebugResources
com.example.user.app-debug-50 /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/packaged_res/debug/packageDebugResources
com.example.user.app-debug-51 /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/flutter_local_notifications/intermediates/packaged_res/debug/packageDebugResources
com.example.user.app-debug-52 /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/fluttertoast/intermediates/packaged_res/debug/packageDebugResources
com.example.user.app-debug-53 /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/path_provider_android/intermediates/packaged_res/debug/packageDebugResources
com.example.user.app-debug-54 /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/packaged_res/debug/packageDebugResources
com.example.user.app-debug-55 /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/shared_preferences_android/intermediates/packaged_res/debug/packageDebugResources
com.example.user.app-debug-56 /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/sqflite_android/intermediates/packaged_res/debug/packageDebugResources
