-- Merging decision tree log ---
application
INJECTED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:11:5-43:19
INJECTED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/debug/AndroidManifest.xml
MERGED from [:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-33:19
MERGED from [:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-33:19
MERGED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:5-46:19
MERGED from [:firebase_core] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-13:19
MERGED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-installations:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8c5f854e79dc68a2edf523fe4f0e5ee9/transformed/jetified-firebase-installations-19.0.0/AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8c5f854e79dc68a2edf523fe4f0e5ee9/transformed/jetified-firebase-installations-19.0.0/AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:22:5-39:19
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/ce8cfb0886867a7af33a5d639c9417b6/transformed/jetified-window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/ce8cfb0886867a7af33a5d639c9417b6/transformed/jetified-window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/e5ef794f53c1b3db91c9205ef71e7cf4/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/e5ef794f53c1b3db91c9205ef71e7cf4/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.12/transforms/1869619aaee8c0e8354b3352b660f578/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.12/transforms/1869619aaee8c0e8354b3352b660f578/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:7:5-20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/a9e8ac145e479222ef1a07acece1be4f/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/a9e8ac145e479222ef1a07acece1be4f/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/be8cf82008cd0efd7327d47911247a50/transformed/jetified-firebase-iid-interop-17.1.0/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/be8cf82008cd0efd7327d47911247a50/transformed/jetified-firebase-iid-interop-17.1.0/AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/bebd35e7e89853ea4a9113a209073011/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/bebd35e7e89853ea4a9113a209073011/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:5:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/bf052f6eddfb9e8ee44604bae73509f2/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/bf052f6eddfb9e8ee44604bae73509f2/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/4c3674e95d4757aa8e62cb4489c3d172/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/4c3674e95d4757aa8e62cb4489c3d172/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/b070f665392501a799c8a6c90c36edfd/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/b070f665392501a799c8a6c90c36edfd/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/cf8e7641e0d34884d0fa18ff00d5bde6/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/cf8e7641e0d34884d0fa18ff00d5bde6/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/88e7bcef79866b370f51d90278a6d617/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/88e7bcef79866b370f51d90278a6d617/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/63cf836f20c0f1dc738cdf6d06cef59d/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/63cf836f20c0f1dc738cdf6d06cef59d/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/6e2cc864b4294b37ed6688e81c623366/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/6e2cc864b4294b37ed6688e81c623366/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:25:5-39:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/46aa997c1a0ab1f5bea86d40262d11ca/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/46aa997c1a0ab1f5bea86d40262d11ca/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/debug/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/b070f665392501a799c8a6c90c36edfd/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
	android:name
		INJECTED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml
manifest
ADDED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:1:1-44:12
MERGED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:1:1-44:12
INJECTED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/debug/AndroidManifest.xml:1:1-7:12
MERGED from [:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-35:12
MERGED from [:shared_preferences_android] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/shared_preferences_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:fluttertoast] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/fluttertoast/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-48:12
MERGED from [:firebase_core] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-15:12
MERGED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-installations:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8c5f854e79dc68a2edf523fe4f0e5ee9/transformed/jetified-firebase-installations-19.0.0/AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:15:1-41:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/e507f6d5eb5275dda970c8593cfa2c86/transformed/jetified-datastore-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/b74446f24719d70707fefbec350004a6/transformed/jetified-datastore-preferences-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/da19215eac54a2bea1d6bc195902ef1f/transformed/jetified-datastore-release/AndroidManifest.xml:17:1-22:12
MERGED from [:flutter_local_notifications] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-10:12
MERGED from [:path_provider_android] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite_android] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/sqflite_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media:media:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/8835dc625064015190b6c0ed66042883/transformed/media-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.preference:preference:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/ab33acf9f8491d391c4b50ceda6aad27/transformed/preference-1.2.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d8b6869b89e3031c57ed06b384b79e6e/transformed/recyclerview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/c5cc50b5e6c960bb3c7ce7a5cfa19b31/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/7df2305af7def53c0baebd27c553faa7/transformed/slidingpanelayout-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/ce8cfb0886867a7af33a5d639c9417b6/transformed/jetified-window-1.2.0/AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/18f4262c3eeb6167bdf47eac4f8e6e1f/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:17:1-21:12
MERGED from [androidx.appcompat:appcompat:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/4e897a92a8589081ccc4806d7917ee6d/transformed/appcompat-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/e5ef794f53c1b3db91c9205ef71e7cf4/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:16:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/48a501c37d3905e479a6a9a1e584fdbb/transformed/jetified-fragment-ktx-1.7.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/611301238bcdcb1e86c512269ca104a3/transformed/fragment-1.7.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.1] /Users/<USER>/.gradle/caches/8.12/transforms/b3cb6375898834fd8b20f57194767be8/transformed/jetified-activity-ktx-1.8.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/8.12/transforms/33e07ea9350fd22c13f24f7a58c5ffc5/transformed/jetified-activity-1.8.1/AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.12/transforms/1869619aaee8c0e8354b3352b660f578/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/f456ad56c38fbd364713044db4e23e86/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/264c05968bdfd6bdbb22c45d6b8f68d0/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/e5ba0e2d74aec3d8c1bca048cd0c2b9f/transformed/jetified-lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/e5b6ea368209bc8c41b2c6ae4fa7b464/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/e6b4f321bb42a880e130349820d2c12f/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/362c748c2afeef6f9542689b59ea464d/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/f6cab6af970b1956ac5a793fb687a128/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/9e4c707523669c03674d0b6e76e75c9e/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/3f3461b291f3734e4292897ea57fd72f/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/438b84687865a785ac5acced4d35c112/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/a9e8ac145e479222ef1a07acece1be4f/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/0f9299326b12e3b7699977d626199805/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/ba7ca02849579a5942bc5e00dce7ee34/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/be8cf82008cd0efd7327d47911247a50/transformed/jetified-firebase-iid-interop-17.1.0/AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c27e2f3c106c2f1814f8db24c225f4d/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/c4f5fc9f34af0cdb3975ee9c4881d983/transformed/jetified-firebase-installations-interop-17.1.1/AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/bebd35e7e89853ea4a9113a209073011/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:2:1-6:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/bf052f6eddfb9e8ee44604bae73509f2/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/4c3674e95d4757aa8e62cb4489c3d172/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:16:1-26:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/bbf4a1abee88aab492d245668ed5fdb2/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/f80dfb8940b4ea060ece7c8198949a6f/transformed/jetified-appcompat-resources-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/c8e77e0b2c4b377e36d2b0eae8f8ea9c/transformed/drawerlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091c4e43d4771808b3a5141d1c246ad/transformed/coordinatorlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/cf655f0a3526238b565ba8896695d53e/transformed/customview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/bbc9215d2fdfe6f42ae8913f3f5289ad/transformed/transition-1.4.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/a015f2f8cef92599656e147a973feddd/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/889d8c03e90dfdd35c0cdf4afce06aec/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4ae95c5841e881c23238fc892169d1/transformed/swiperefreshlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/786a0362899715a0cd994cce7910a84f/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/b070f665392501a799c8a6c90c36edfd/transformed/core-1.13.1/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/16ddbb077446d9c6f09bab560a572eaa/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/2f9c75fb3ff07661e4d00855f2f6b792/transformed/localbroadcastmanager-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/cf8e7641e0d34884d0fa18ff00d5bde6/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/eeb836bac5c9af717aebd341ad53ada4/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8a0b239be9de2f31ac130cabce5885c3/transformed/jetified-firebase-components-19.0.0/AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/88e7bcef79866b370f51d90278a6d617/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/63cf836f20c0f1dc738cdf6d06cef59d/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/10eea5bb65bf94170ba046bf1367a841/transformed/jetified-firebase-encoders-json-18.0.0/AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/6e2cc864b4294b37ed6688e81c623366/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/9b3cfdf47699eab1c78caefbd95ccdc4/transformed/jetified-transport-api-3.1.0/AndroidManifest.xml:15:1-20:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e2e3e6b87481820aad8c092aa113f366/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/46aa997c1a0ab1f5bea86d40262d11ca/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/dbccd487fa973b76ae556af0c8ed0b74/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/079a19cc7a2eb64cd7c25bbdcd2bbde6/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/4ddb0b5c244f312bc4e2894477ecd4fe/transformed/jetified-core-1.0.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/06b5e494080ac3cc6508518ade9ca032/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1eccd8381f90f5a56556d3fd469f6e25/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/f69c4d7e62ac92eb85be6d4211abb555/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.12/transforms/343edef1cef647b48596c092b764f8b4/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:2:1-7:12
	package
		INJECTED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/debug/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/debug/AndroidManifest.xml
	android:versionCode
		INJECTED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/debug/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:2:1-63
MERGED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:2:1-63
MERGED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:2:1-63
MERGED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-67
MERGED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-installations:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8c5f854e79dc68a2edf523fe4f0e5ee9/transformed/jetified-firebase-installations-19.0.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8c5f854e79dc68a2edf523fe4f0e5ee9/transformed/jetified-firebase-installations-19.0.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c27e2f3c106c2f1814f8db24c225f4d/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c27e2f3c106c2f1814f8db24c225f4d/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/63cf836f20c0f1dc738cdf6d06cef59d/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/63cf836f20c0f1dc738cdf6d06cef59d/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:25:5-67
	android:name
		ADDED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:2:18-60
uses-permission#android.permission.CAMERA
ADDED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:3:1-61
	android:name
		ADDED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:3:18-58
uses-feature#android.hardware.camera
ADDED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:4:1-56
	android:name
		ADDED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:4:15-53
uses-feature#android.hardware.camera.autofocus
ADDED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:5:1-66
	android:name
		ADDED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:5:15-63
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:6:1-77
	android:name
		ADDED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:6:18-74
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:7:1-76
	android:name
		ADDED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:7:18-73
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:8:1-76
	android:name
		ADDED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:8:18-74
uses-permission#android.permission.RECEIVE
ADDED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:9:1-61
	android:name
		ADDED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/main/AndroidManifest.xml:9:18-59
uses-sdk
INJECTED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/debug/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/debug/AndroidManifest.xml
INJECTED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/debug/AndroidManifest.xml
MERGED from [:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/shared_preferences_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/shared_preferences_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:fluttertoast] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/fluttertoast/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:fluttertoast] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/fluttertoast/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8c5f854e79dc68a2edf523fe4f0e5ee9/transformed/jetified-firebase-installations-19.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8c5f854e79dc68a2edf523fe4f0e5ee9/transformed/jetified-firebase-installations-19.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/e507f6d5eb5275dda970c8593cfa2c86/transformed/jetified-datastore-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/e507f6d5eb5275dda970c8593cfa2c86/transformed/jetified-datastore-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/b74446f24719d70707fefbec350004a6/transformed/jetified-datastore-preferences-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/b74446f24719d70707fefbec350004a6/transformed/jetified-datastore-preferences-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/da19215eac54a2bea1d6bc195902ef1f/transformed/jetified-datastore-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/da19215eac54a2bea1d6bc195902ef1f/transformed/jetified-datastore-release/AndroidManifest.xml:20:5-44
MERGED from [:flutter_local_notifications] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/sqflite_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/sqflite_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/8835dc625064015190b6c0ed66042883/transformed/media-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/8835dc625064015190b6c0ed66042883/transformed/media-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/ab33acf9f8491d391c4b50ceda6aad27/transformed/preference-1.2.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/ab33acf9f8491d391c4b50ceda6aad27/transformed/preference-1.2.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d8b6869b89e3031c57ed06b384b79e6e/transformed/recyclerview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d8b6869b89e3031c57ed06b384b79e6e/transformed/recyclerview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/c5cc50b5e6c960bb3c7ce7a5cfa19b31/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/c5cc50b5e6c960bb3c7ce7a5cfa19b31/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/7df2305af7def53c0baebd27c553faa7/transformed/slidingpanelayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/7df2305af7def53c0baebd27c553faa7/transformed/slidingpanelayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/ce8cfb0886867a7af33a5d639c9417b6/transformed/jetified-window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/ce8cfb0886867a7af33a5d639c9417b6/transformed/jetified-window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/18f4262c3eeb6167bdf47eac4f8e6e1f/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/18f4262c3eeb6167bdf47eac4f8e6e1f/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.appcompat:appcompat:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/4e897a92a8589081ccc4806d7917ee6d/transformed/appcompat-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/4e897a92a8589081ccc4806d7917ee6d/transformed/appcompat-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/e5ef794f53c1b3db91c9205ef71e7cf4/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/e5ef794f53c1b3db91c9205ef71e7cf4/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment-ktx:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/48a501c37d3905e479a6a9a1e584fdbb/transformed/jetified-fragment-ktx-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/48a501c37d3905e479a6a9a1e584fdbb/transformed/jetified-fragment-ktx-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/611301238bcdcb1e86c512269ca104a3/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/611301238bcdcb1e86c512269ca104a3/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] /Users/<USER>/.gradle/caches/8.12/transforms/b3cb6375898834fd8b20f57194767be8/transformed/jetified-activity-ktx-1.8.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] /Users/<USER>/.gradle/caches/8.12/transforms/b3cb6375898834fd8b20f57194767be8/transformed/jetified-activity-ktx-1.8.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/8.12/transforms/33e07ea9350fd22c13f24f7a58c5ffc5/transformed/jetified-activity-1.8.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/8.12/transforms/33e07ea9350fd22c13f24f7a58c5ffc5/transformed/jetified-activity-1.8.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.12/transforms/1869619aaee8c0e8354b3352b660f578/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.12/transforms/1869619aaee8c0e8354b3352b660f578/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/f456ad56c38fbd364713044db4e23e86/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/f456ad56c38fbd364713044db4e23e86/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/264c05968bdfd6bdbb22c45d6b8f68d0/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/264c05968bdfd6bdbb22c45d6b8f68d0/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/e5ba0e2d74aec3d8c1bca048cd0c2b9f/transformed/jetified-lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/e5ba0e2d74aec3d8c1bca048cd0c2b9f/transformed/jetified-lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/e5b6ea368209bc8c41b2c6ae4fa7b464/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/e5b6ea368209bc8c41b2c6ae4fa7b464/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/e6b4f321bb42a880e130349820d2c12f/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/e6b4f321bb42a880e130349820d2c12f/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/362c748c2afeef6f9542689b59ea464d/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/362c748c2afeef6f9542689b59ea464d/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/f6cab6af970b1956ac5a793fb687a128/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/f6cab6af970b1956ac5a793fb687a128/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/9e4c707523669c03674d0b6e76e75c9e/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/9e4c707523669c03674d0b6e76e75c9e/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/3f3461b291f3734e4292897ea57fd72f/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/3f3461b291f3734e4292897ea57fd72f/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/438b84687865a785ac5acced4d35c112/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/438b84687865a785ac5acced4d35c112/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/a9e8ac145e479222ef1a07acece1be4f/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/a9e8ac145e479222ef1a07acece1be4f/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/0f9299326b12e3b7699977d626199805/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/0f9299326b12e3b7699977d626199805/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/ba7ca02849579a5942bc5e00dce7ee34/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/ba7ca02849579a5942bc5e00dce7ee34/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/be8cf82008cd0efd7327d47911247a50/transformed/jetified-firebase-iid-interop-17.1.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/be8cf82008cd0efd7327d47911247a50/transformed/jetified-firebase-iid-interop-17.1.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c27e2f3c106c2f1814f8db24c225f4d/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c27e2f3c106c2f1814f8db24c225f4d/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/c4f5fc9f34af0cdb3975ee9c4881d983/transformed/jetified-firebase-installations-interop-17.1.1/AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/c4f5fc9f34af0cdb3975ee9c4881d983/transformed/jetified-firebase-installations-interop-17.1.1/AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/bebd35e7e89853ea4a9113a209073011/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/bebd35e7e89853ea4a9113a209073011/transformed/jetified-play-services-tasks-18.1.0/AndroidManifest.xml:4:5-43
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/bf052f6eddfb9e8ee44604bae73509f2/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/bf052f6eddfb9e8ee44604bae73509f2/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/4c3674e95d4757aa8e62cb4489c3d172/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/4c3674e95d4757aa8e62cb4489c3d172/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:18:5-43
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/bbf4a1abee88aab492d245668ed5fdb2/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/bbf4a1abee88aab492d245668ed5fdb2/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/f80dfb8940b4ea060ece7c8198949a6f/transformed/jetified-appcompat-resources-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/f80dfb8940b4ea060ece7c8198949a6f/transformed/jetified-appcompat-resources-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/c8e77e0b2c4b377e36d2b0eae8f8ea9c/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/c8e77e0b2c4b377e36d2b0eae8f8ea9c/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091c4e43d4771808b3a5141d1c246ad/transformed/coordinatorlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091c4e43d4771808b3a5141d1c246ad/transformed/coordinatorlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/cf655f0a3526238b565ba8896695d53e/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/cf655f0a3526238b565ba8896695d53e/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/bbc9215d2fdfe6f42ae8913f3f5289ad/transformed/transition-1.4.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/bbc9215d2fdfe6f42ae8913f3f5289ad/transformed/transition-1.4.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/a015f2f8cef92599656e147a973feddd/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/a015f2f8cef92599656e147a973feddd/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/889d8c03e90dfdd35c0cdf4afce06aec/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/889d8c03e90dfdd35c0cdf4afce06aec/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4ae95c5841e881c23238fc892169d1/transformed/swiperefreshlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/9a4ae95c5841e881c23238fc892169d1/transformed/swiperefreshlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/786a0362899715a0cd994cce7910a84f/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/786a0362899715a0cd994cce7910a84f/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/b070f665392501a799c8a6c90c36edfd/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/b070f665392501a799c8a6c90c36edfd/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/16ddbb077446d9c6f09bab560a572eaa/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/16ddbb077446d9c6f09bab560a572eaa/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/2f9c75fb3ff07661e4d00855f2f6b792/transformed/localbroadcastmanager-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/2f9c75fb3ff07661e4d00855f2f6b792/transformed/localbroadcastmanager-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/cf8e7641e0d34884d0fa18ff00d5bde6/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/cf8e7641e0d34884d0fa18ff00d5bde6/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/eeb836bac5c9af717aebd341ad53ada4/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/eeb836bac5c9af717aebd341ad53ada4/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8a0b239be9de2f31ac130cabce5885c3/transformed/jetified-firebase-components-19.0.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8a0b239be9de2f31ac130cabce5885c3/transformed/jetified-firebase-components-19.0.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/88e7bcef79866b370f51d90278a6d617/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/88e7bcef79866b370f51d90278a6d617/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/63cf836f20c0f1dc738cdf6d06cef59d/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/63cf836f20c0f1dc738cdf6d06cef59d/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/10eea5bb65bf94170ba046bf1367a841/transformed/jetified-firebase-encoders-json-18.0.0/AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/10eea5bb65bf94170ba046bf1367a841/transformed/jetified-firebase-encoders-json-18.0.0/AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/6e2cc864b4294b37ed6688e81c623366/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/6e2cc864b4294b37ed6688e81c623366/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/9b3cfdf47699eab1c78caefbd95ccdc4/transformed/jetified-transport-api-3.1.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/9b3cfdf47699eab1c78caefbd95ccdc4/transformed/jetified-transport-api-3.1.0/AndroidManifest.xml:18:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e2e3e6b87481820aad8c092aa113f366/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e2e3e6b87481820aad8c092aa113f366/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/46aa997c1a0ab1f5bea86d40262d11ca/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/46aa997c1a0ab1f5bea86d40262d11ca/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/dbccd487fa973b76ae556af0c8ed0b74/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/dbccd487fa973b76ae556af0c8ed0b74/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/079a19cc7a2eb64cd7c25bbdcd2bbde6/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/079a19cc7a2eb64cd7c25bbdcd2bbde6/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/4ddb0b5c244f312bc4e2894477ecd4fe/transformed/jetified-core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/4ddb0b5c244f312bc4e2894477ecd4fe/transformed/jetified-core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/06b5e494080ac3cc6508518ade9ca032/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/06b5e494080ac3cc6508518ade9ca032/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1eccd8381f90f5a56556d3fd469f6e25/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1eccd8381f90f5a56556d3fd469f6e25/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/f69c4d7e62ac92eb85be6d4211abb555/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/f69c4d7e62ac92eb85be6d4211abb555/transformed/jetified-annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.12/transforms/343edef1cef647b48596c092b764f8b4/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.12/transforms/343edef1cef647b48596c092b764f8b4/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:5:5-43
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/debug/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/android/app/src/debug/AndroidManifest.xml
provider#dev.fluttercommunity.plus.share.ShareFileProvider
ADDED from [:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:9-21:20
	android:grantUriPermissions
		ADDED from [:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:13-47
	android:authorities
		ADDED from [:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-74
	android:exported
		ADDED from [:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-37
	android:name
		ADDED from [:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-77
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:13-20:68
	android:resource
		ADDED from [:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:17-65
	android:name
		ADDED from [:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:17-67
receiver#dev.fluttercommunity.plus.share.SharePlusPendingIntent
ADDED from [:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:9-32:20
	android:exported
		ADDED from [:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:13-37
	android:name
		ADDED from [:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:13-82
intent-filter#action:name:EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:13-31:29
action#EXTRA_CHOSEN_COMPONENT
ADDED from [:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:17-65
	android:name
		ADDED from [:share_plus] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/share_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:25-62
uses-permission#android.permission.WAKE_LOCK
ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-68
MERGED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c27e2f3c106c2f1814f8db24c225f4d/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c27e2f3c106c2f1814f8db24c225f4d/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:9:5-68
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:22-65
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:5-79
MERGED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8c5f854e79dc68a2edf523fe4f0e5ee9/transformed/jetified-firebase-installations-19.0.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8c5f854e79dc68a2edf523fe4f0e5ee9/transformed/jetified-firebase-installations-19.0.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c27e2f3c106c2f1814f8db24c225f4d/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c27e2f3c106c2f1814f8db24c225f4d/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/63cf836f20c0f1dc738cdf6d06cef59d/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/63cf836f20c0f1dc738cdf6d06cef59d/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/6e2cc864b4294b37ed6688e81c623366/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/6e2cc864b4294b37ed6688e81c623366/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:22:5-79
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:22-76
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:5-77
MERGED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:23:5-77
MERGED from [:flutter_local_notifications] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-77
MERGED from [:flutter_local_notifications] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-77
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:22-74
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService
ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:9-17:72
	android:exported
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-37
	android:permission
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:13-69
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-107
service#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService
ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:9-24:19
	android:exported
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:13-37
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:13-97
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:13-23:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:17-78
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:25-75
receiver#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver
ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:9-33:20
	android:exported
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:13-36
	android:permission
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:13-73
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:13-98
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:13-32:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:31:17-81
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:31:25-78
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:35:9-39:19
MERGED from [:firebase_core] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-12:19
MERGED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8c5f854e79dc68a2edf523fe4f0e5ee9/transformed/jetified-firebase-installations-19.0.0/AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8c5f854e79dc68a2edf523fe4f0e5ee9/transformed/jetified-firebase-installations-19.0.0/AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/88e7bcef79866b370f51d90278a6d617/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/88e7bcef79866b370f51d90278a6d617/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:35:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar
ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:36:13-38:85
	android:value
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:38:17-82
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:37:17-128
provider#io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider
ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:41:9-45:38
	android:authorities
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:43:13-88
	android:exported
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:44:13-37
	android:initOrder
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:45:13-35
	android:name
		ADDED from [:firebase_messaging] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_messaging/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:42:13-102
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/firebase_core/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:17-124
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c27e2f3c106c2f1814f8db24c225f4d/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c27e2f3c106c2f1814f8db24c225f4d/transformed/jetified-play-services-cloud-messaging-17.2.0/AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:30:13-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:47:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:25.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6ab185ff56d488a955de569500fe91c8/transformed/jetified-firebase-messaging-25.0.0/AndroidManifest.xml:61:17-119
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8c5f854e79dc68a2edf523fe4f0e5ee9/transformed/jetified-firebase-installations-19.0.0/AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8c5f854e79dc68a2edf523fe4f0e5ee9/transformed/jetified-firebase-installations-19.0.0/AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8c5f854e79dc68a2edf523fe4f0e5ee9/transformed/jetified-firebase-installations-19.0.0/AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8c5f854e79dc68a2edf523fe4f0e5ee9/transformed/jetified-firebase-installations-19.0.0/AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8c5f854e79dc68a2edf523fe4f0e5ee9/transformed/jetified-firebase-installations-19.0.0/AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8c5f854e79dc68a2edf523fe4f0e5ee9/transformed/jetified-firebase-installations-19.0.0/AndroidManifest.xml:19:17-127
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:22.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/627ce18d8ba4416b68561dd69e993e92/transformed/jetified-firebase-common-22.0.0/AndroidManifest.xml:36:17-109
uses-permission#android.permission.VIBRATE
ADDED from [:flutter_local_notifications] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-66
	android:name
		ADDED from [:flutter_local_notifications] /Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/flutter_local_notifications/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:22-63
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/ce8cfb0886867a7af33a5d639c9417b6/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/ce8cfb0886867a7af33a5d639c9417b6/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/ce8cfb0886867a7af33a5d639c9417b6/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/ce8cfb0886867a7af33a5d639c9417b6/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/ce8cfb0886867a7af33a5d639c9417b6/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/ce8cfb0886867a7af33a5d639c9417b6/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/e5ef794f53c1b3db91c9205ef71e7cf4/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/e5ef794f53c1b3db91c9205ef71e7cf4/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/e5ef794f53c1b3db91c9205ef71e7cf4/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/e5ef794f53c1b3db91c9205ef71e7cf4/transformed/jetified-play-services-base-18.1.0/AndroidManifest.xml:20:19-85
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/a9e8ac145e479222ef1a07acece1be4f/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/cf8e7641e0d34884d0fa18ff00d5bde6/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/cf8e7641e0d34884d0fa18ff00d5bde6/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/a9e8ac145e479222ef1a07acece1be4f/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/a9e8ac145e479222ef1a07acece1be4f/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/a9e8ac145e479222ef1a07acece1be4f/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/a9e8ac145e479222ef1a07acece1be4f/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/a9e8ac145e479222ef1a07acece1be4f/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/a9e8ac145e479222ef1a07acece1be4f/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/a9e8ac145e479222ef1a07acece1be4f/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/4c3674e95d4757aa8e62cb4489c3d172/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/4c3674e95d4757aa8e62cb4489c3d172/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/4c3674e95d4757aa8e62cb4489c3d172/transformed/jetified-play-services-basement-18.3.0/AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/b070f665392501a799c8a6c90c36edfd/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/b070f665392501a799c8a6c90c36edfd/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/b070f665392501a799c8a6c90c36edfd/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
permission#com.example.user.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/b070f665392501a799c8a6c90c36edfd/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/b070f665392501a799c8a6c90c36edfd/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/b070f665392501a799c8a6c90c36edfd/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/b070f665392501a799c8a6c90c36edfd/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/b070f665392501a799c8a6c90c36edfd/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
uses-permission#com.example.user.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/b070f665392501a799c8a6c90c36edfd/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/b070f665392501a799c8a6c90c36edfd/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/6a1d3c9123932770773e0b1c0a2dd3a1/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/88e7bcef79866b370f51d90278a6d617/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/88e7bcef79866b370f51d90278a6d617/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/88e7bcef79866b370f51d90278a6d617/transformed/jetified-firebase-datatransport-18.2.0/AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/63cf836f20c0f1dc738cdf6d06cef59d/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/6e2cc864b4294b37ed6688e81c623366/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/6e2cc864b4294b37ed6688e81c623366/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/63cf836f20c0f1dc738cdf6d06cef59d/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/63cf836f20c0f1dc738cdf6d06cef59d/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/63cf836f20c0f1dc738cdf6d06cef59d/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/63cf836f20c0f1dc738cdf6d06cef59d/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/63cf836f20c0f1dc738cdf6d06cef59d/transformed/jetified-transport-backend-cct-3.1.9/AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/6e2cc864b4294b37ed6688e81c623366/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/6e2cc864b4294b37ed6688e81c623366/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/6e2cc864b4294b37ed6688e81c623366/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/6e2cc864b4294b37ed6688e81c623366/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/6e2cc864b4294b37ed6688e81c623366/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/6e2cc864b4294b37ed6688e81c623366/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] /Users/<USER>/.gradle/caches/8.12/transforms/6e2cc864b4294b37ed6688e81c623366/transformed/jetified-transport-runtime-3.1.9/AndroidManifest.xml:33:13-132
