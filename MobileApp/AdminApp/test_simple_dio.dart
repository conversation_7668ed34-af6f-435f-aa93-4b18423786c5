import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';

Future<void> main() async {
  print('🔍 Testing Simple Dio HTTP/2 Configuration');
  print('==============================================');

  await testDioWithHttp2Config();
}

Future<void> testDioWithHttp2Config() async {
  print('\n📱 Testing Dio with HTTP/2 and SSL Configuration');
  print('================================================');

  try {
    final dio = Dio();

    // Configure timeouts
    dio.options.connectTimeout = const Duration(seconds: 30);
    dio.options.receiveTimeout = const Duration(seconds: 30);
    dio.options.sendTimeout = const Duration(seconds: 30);

    // Configure headers to match curl
    dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': 'curl/8.7.1',
    };

    // Configure HTTP client for better SSL handling
    (dio.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () {
      final client = HttpClient();
      client.connectionTimeout = const Duration(seconds: 30);
      client.idleTimeout = const Duration(seconds: 30);

      // Configure SSL settings
      client.badCertificateCallback = (cert, host, port) {
        print('SSL Certificate for $host:$port - Subject: ${cert.subject}');
        return true; // Accept all certificates for testing
      };

      return client;
    };

    // Add logging interceptor
    dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: false, // Don't log full response to avoid clutter
      error: true,
      logPrint: (object) => print('DIO: $object'),
    ));

    print('   🔄 Making API call to admin login...');

    final response = await dio.post(
      'https://zoomfresh.co.in/api/admin/login',
      data: {
        'email': '<EMAIL>',
        'password': 'password123',
        'fcm_token': 'test_token',
      },
    );

    print('   ✅ Status Code: ${response.statusCode}');

    if (response.statusCode == 200) {
      print('   🎉 SUCCESS: Dio HTTP/2 configuration works!');
      final responseData = response.data;
      if (responseData is Map && responseData['status'] == 'success') {
        print('   📄 Login successful: ${responseData['message']}');
      } else {
        print('   📄 Response: ${responseData.toString().substring(0, 100)}...');
      }
    } else {
      print('   ⚠️  Unexpected status: ${response.statusCode}');
      print('   📄 Response: ${response.data}');
    }
  } catch (e) {
    print('   ❌ ERROR: $e');
    if (e is DioException) {
      print('   📊 Dio Error Type: ${e.type}');
      print('   📊 Dio Error Message: ${e.message}');
      if (e.response != null) {
        print('   📊 Response Status: ${e.response?.statusCode}');
      }
    }
  }
}
