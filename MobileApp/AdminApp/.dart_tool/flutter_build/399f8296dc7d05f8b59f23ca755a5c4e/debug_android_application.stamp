{"inputs": ["/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/.dart_tool/flutter_build/399f8296dc7d05f8b59f23ca755a5c4e/app.dill", "/Users/<USER>/development/flutter/packages/flutter_tools/lib/src/build_system/targets/icon_tree_shaker.dart", "/Users/<USER>/development/flutter/bin/cache/engine.stamp", "/Users/<USER>/development/flutter/bin/cache/engine.stamp", "/Users/<USER>/development/flutter/bin/cache/engine.stamp", "/Users/<USER>/development/flutter/bin/cache/engine.stamp", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/pubspec.yaml", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/images/flash.png", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/images/.DS_Store", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/images/wallpaper.png", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/images/place.webp", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/images/barcode.webp", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/images/user.png", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/images/pro.webp", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/images/Asset 10xxxhdpi.png", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/images/sub.webp", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/images/logo3.png", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/images/logo.png", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/images/logo2.png", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/images/wallet.webp", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/images/Barcode-Scanner-1.jpg", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/images/green-check-mark-icon-on-transparent-background-free-png.webp", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/images/g.png", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/images/p.png", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/images/orders.png", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/fonts/Neuton-Regular.ttf", "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf", "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.10/assets/toastify.js", "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.10/assets/toastify.css", "/Users/<USER>/development/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "/Users/<USER>/development/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/.dart_tool/flutter_build/399f8296dc7d05f8b59f23ca755a5c4e/native_assets.json", "/Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.60/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/args-2.6.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.9.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-4.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-6.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-3.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-16.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.7.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-4.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-18.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-5.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-8.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.10/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.15/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-10.1.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-5.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.3.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4+6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.3.0+3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.10.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.10.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/LICENSE", "/Users/<USER>/development/flutter/bin/cache/pkg/sky_engine/LICENSE", "/Users/<USER>/development/flutter/packages/flutter/LICENSE", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/DOES_NOT_EXIST_RERUN_FOR_WILDCARD299637160"], "outputs": ["/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/vm_snapshot_data", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/isolate_snapshot_data", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/kernel_blob.bin", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/images/flash.png", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/images/.DS_Store", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/images/wallpaper.png", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/images/place.webp", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/images/barcode.webp", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/images/user.png", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/images/pro.webp", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/images/Asset%2010xxxhdpi.png", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/images/sub.webp", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/images/logo3.png", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/images/logo.png", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/images/logo2.png", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/images/wallet.webp", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/images/Barcode-Scanner-1.jpg", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/images/green-check-mark-icon-on-transparent-background-free-png.webp", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/images/g.png", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/images/p.png", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/images/orders.png", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/fonts/Neuton-Regular.ttf", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/packages/fluttertoast/assets/toastify.js", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/packages/fluttertoast/assets/toastify.css", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/fonts/MaterialIcons-Regular.otf", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/shaders/ink_sparkle.frag", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z", "/Users/<USER>/Desktop/Maaz/ZoomFresh/MobileApp/AdminApp/build/app/intermediates/flutter/debug/flutter_assets/NativeAssetsManifest.json"]}